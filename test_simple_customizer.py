#!/usr/bin/env python3
"""
Test the simple taskbar customizer
"""

import subprocess

def test_simple_customizer():
    """Test the simple customizer"""
    print("Simple Taskbar Customizer Test")
    print("BY Aladdin Shenewa")
    print("=" * 35)
    
    print("🎯 WHAT THIS DOES:")
    print("✅ Changes the FULL taskbar background color")
    print("✅ Adds working music controls")
    print("✅ Adds settings and theme buttons")
    print("✅ Includes revert button")
    print("✅ Much simpler and more reliable")
    print()
    
    print("🎨 VISUAL RESULT:")
    print("Your entire taskbar will change to your theme color")
    print("Controls will be integrated and working")
    print("No glitches or overlay issues")
    print()
    
    print("🔧 CONTROLS:")
    print("🎵 Music: ▶️ Play, ⏸️ Pause, ⏹️ Stop")
    print("⚙️ Settings: Opens WindowsCustom settings")
    print("🎨 Themes: Quick theme switcher")
    print("🔄 Revert: Instantly revert all changes")
    print()
    
    choice = input("Start the simple taskbar customizer? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n🚀 Starting simple taskbar customizer...")
        print("Your taskbar will change color and get new controls!")
        
        try:
            subprocess.run(["python", "simple_taskbar_customizer.py"])
        except KeyboardInterrupt:
            print("\nCustomizer stopped")
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("Test cancelled")

def main():
    """Main function"""
    test_simple_customizer()

if __name__ == "__main__":
    main()
