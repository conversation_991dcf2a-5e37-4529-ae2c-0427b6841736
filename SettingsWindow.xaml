<Window x:Class="WindowsCustom.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="WindowsCustom Settings" Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        Background="#2D2D2D">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title -->
        <TextBlock Grid.Row="0" Text="WindowsCustom Settings" 
                   FontSize="24" FontWeight="Bold" 
                   Foreground="White" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- Settings Content -->
        <TabControl Grid.Row="1" Background="#3D3D3D" BorderBrush="#555555">
            <TabItem Header="General" Foreground="White">
                <StackPanel Margin="20">
                    <CheckBox Content="Start with Windows" Foreground="White" Margin="0,10"/>
                    <CheckBox Content="Show notifications" Foreground="White" Margin="0,10"/>
                    <CheckBox Content="Auto-hide when not in use" Foreground="White" Margin="0,10"/>
                </StackPanel>
            </TabItem>
            
            <TabItem Header="Music" Foreground="White">
                <StackPanel Margin="20">
                    <TextBlock Text="Music Folder:" Foreground="White" Margin="0,10,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Text="./music" Margin="0,0,10,0"/>
                        <Button Grid.Column="1" Content="Browse..." Width="80"/>
                    </Grid>
                    <CheckBox Content="Shuffle playback" Foreground="White" Margin="0,20,0,10"/>
                    <CheckBox Content="Repeat playlist" Foreground="White" Margin="0,10"/>
                </StackPanel>
            </TabItem>
            
            <TabItem Header="Appearance" Foreground="White">
                <StackPanel Margin="20">
                    <TextBlock Text="Taskbar Height:" Foreground="White" Margin="0,10,0,5"/>
                    <Slider Minimum="40" Maximum="80" Value="48" Margin="0,0,0,20"/>
                    <CheckBox Content="Show app icons" Foreground="White" Margin="0,10"/>
                    <CheckBox Content="Show clock" Foreground="White" Margin="0,10"/>
                </StackPanel>
            </TabItem>
        </TabControl>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Apply" Width="80" Height="30" 
                   Background="#4CAF50" Foreground="White" 
                   BorderBrush="#45a049" Margin="0,0,10,0"/>
            <Button Content="Cancel" Width="80" Height="30" 
                   Background="#666666" Foreground="White" 
                   BorderBrush="#888888"
                   Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
