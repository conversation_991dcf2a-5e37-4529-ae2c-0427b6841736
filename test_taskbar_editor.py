#!/usr/bin/env python3
"""
Test the taskbar editor functionality
"""

import win32gui

def test_taskbar_detection():
    """Test if we can detect the Windows taskbar"""
    print("Testing Windows Taskbar Detection")
    print("=" * 40)
    
    try:
        # Find taskbar
        taskbar = win32gui.FindWindow("Shell_TrayWnd", None)
        
        if taskbar:
            print(f"✅ Windows taskbar found: {taskbar}")
            
            # Get taskbar position
            rect = win32gui.GetWindowRect(taskbar)
            print(f"📍 Position: ({rect[0]}, {rect[1]}) to ({rect[2]}, {rect[3]})")
            print(f"📏 Size: {rect[2] - rect[0]} x {rect[3] - rect[1]}")
            
            # Check if taskbar is visible
            if win32gui.IsWindowVisible(taskbar):
                print("👁️ Taskbar is visible")
            else:
                print("👁️ Taskbar is hidden")
            
            return True
        else:
            print("❌ Windows taskbar not found")
            return False
            
    except Exception as e:
        print(f"❌ Error detecting taskbar: {e}")
        return False

def test_overlay_positioning():
    """Test overlay positioning calculations"""
    print("\nTesting Overlay Positioning")
    print("=" * 30)
    
    try:
        taskbar = win32gui.FindWindow("Shell_TrayWnd", None)
        if not taskbar:
            print("❌ No taskbar found for positioning test")
            return False
        
        rect = win32gui.GetWindowRect(taskbar)
        taskbar_info = {
            'left': rect[0], 'top': rect[1],
            'right': rect[2], 'bottom': rect[3],
            'width': rect[2] - rect[0],
            'height': rect[3] - rect[1]
        }
        
        print("📊 Taskbar Info:")
        print(f"   Left: {taskbar_info['left']}")
        print(f"   Top: {taskbar_info['top']}")
        print(f"   Width: {taskbar_info['width']}")
        print(f"   Height: {taskbar_info['height']}")
        
        # Calculate music controls position
        music_width = 100
        music_x = taskbar_info['left'] + 250  # After start button
        music_y = taskbar_info['top'] + 3
        
        print(f"\n🎵 Music Controls Position:")
        print(f"   X: {music_x}, Y: {music_y}")
        print(f"   Size: {music_width} x {taskbar_info['height'] - 6}")
        
        # Calculate settings position
        settings_width = 120
        settings_x = taskbar_info['right'] - settings_width - 100
        settings_y = taskbar_info['top'] + 3
        
        print(f"\n⚙️ Settings Position:")
        print(f"   X: {settings_x}, Y: {settings_y}")
        print(f"   Size: {settings_width} x {taskbar_info['height'] - 6}")
        
        return True
        
    except Exception as e:
        print(f"❌ Positioning test error: {e}")
        return False

def test_theme_loading():
    """Test theme loading"""
    print("\nTesting Theme Loading")
    print("=" * 25)
    
    try:
        import json
        import os
        
        if os.path.exists("settings.json"):
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            current_theme = settings.get("current_theme", "Unknown")
            themes = settings.get("themes", {})
            
            print(f"✅ Settings loaded")
            print(f"🎨 Current theme: {current_theme}")
            print(f"📋 Available themes: {list(themes.keys())}")
            
            if current_theme in themes:
                theme = themes[current_theme]
                print(f"\n🎨 {current_theme} Theme Colors:")
                print(f"   Background: {theme['background']}")
                print(f"   Buttons: {theme['buttons']}")
                print(f"   Text: {theme['text']}")
            
            return True
        else:
            print("❌ settings.json not found")
            return False
            
    except Exception as e:
        print(f"❌ Theme loading error: {e}")
        return False

def test_music_controls():
    """Test music controls import"""
    print("\nTesting Music Controls")
    print("=" * 25)
    
    try:
        import music_controls
        
        controller = music_controls.get_music_controller()
        print("✅ Music controller loaded")
        
        status = controller.get_status()
        print(f"📊 Music status: {status}")
        
        return True
        
    except ImportError:
        print("❌ music_controls module not found")
        return False
    except Exception as e:
        print(f"❌ Music controls error: {e}")
        return False

def demo_taskbar_editor():
    """Demo the taskbar editor"""
    print("\n" + "=" * 50)
    print("TASKBAR EDITOR DEMO")
    print("=" * 50)
    
    print("\n🎯 WHAT THE TASKBAR EDITOR DOES:")
    print("✅ Keeps your existing Windows taskbar")
    print("✅ Adds music controls overlay (▶️⏸️⏹️)")
    print("✅ Adds settings overlay (⚙️🎨)")
    print("✅ Supports your custom themes")
    print("✅ No replacement - just enhancement!")
    
    print("\n📍 OVERLAY POSITIONS:")
    print("┌─Start─┬─Search─┬─🎵Music─┬─────Apps─────┬─⚙️Settings─┬─System─┐")
    print("│   ⊞   │   🔍   │ ▶️⏸️⏹️  │ Chrome VSCode │   ⚙️🎨    │ 🔋Time │")
    print("└───────┴────────┴─────────┴──────────────┴────────────┴────────┘")
    print("  ^Windows^      ^Added^                   ^Added^     ^Windows^")
    
    print("\n🚀 TO RUN:")
    print("1. Simple version: python simple_taskbar_enhancer.py")
    print("2. Full version:   python taskbar_editor.py")
    
    choice = input("\nRun simple taskbar enhancer now? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n🚀 Starting simple taskbar enhancer...")
        print("This will add overlays to your existing taskbar")
        print("Press Ctrl+C to stop")
        
        try:
            import subprocess
            subprocess.run(["python", "simple_taskbar_enhancer.py"])
        except KeyboardInterrupt:
            print("\nTaskbar enhancer stopped")
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("Demo cancelled")

def main():
    """Main test function"""
    print("WindowsCustom Taskbar Editor Test")
    print("BY Aladdin Shenewa")
    print("=" * 50)
    
    # Run tests
    tests = [
        test_taskbar_detection,
        test_overlay_positioning,
        test_theme_loading,
        test_music_controls
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 TEST RESULTS: {passed}/{len(tests)} passed")
    
    if passed == len(tests):
        print("✅ All tests passed! Taskbar editor ready to use.")
        demo_taskbar_editor()
    else:
        print("❌ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
