import json
import tkinter as tk
from tkinter import messagebox

CONFIG_FILE = "settings.json"

def load_settings():
    import json
    try:
        with open(CONFIG_FILE, "r") as f:
            return json.load(f)
    except:
        return {}

def save_settings(settings):
    with open(CONFIG_FILE, "w") as f:
        json.dump(settings, f, indent=2)

class SettingsUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("WindowsCustom Settings")
        self.geometry("400x400")

        self.settings = load_settings()

        self.run_on_start_var = tk.BooleanVar(value=self.settings.get("run_on_start", True))
        self.enable_music_var = tk.BooleanVar(value=self.settings.get("enable_music", True))
        self.enable_backup_var = tk.BooleanVar(value=self.settings.get("enable_backup", True))
        self.fake_bsod_var = tk.BooleanVar(value=self.settings.get("fake_bsod_enabled", True))

        tk.Checkbutton(self, text="Run on Startup", variable=self.run_on_start_var).pack(pady=5)
        tk.Checkbutton(self, text="Enable Music Player", variable=self.enable_music_var).pack(pady=5)
        tk.Checkbutton(self, text="Enable Smart Backup", variable=self.enable_backup_var).pack(pady=5)
        tk.Checkbutton(self, text="Enable Fake BSOD", variable=self.fake_bsod_var).pack(pady=5)

        tk.Button(self, text="Save", command=self.save).pack(pady=20)

    def save(self):
        self.settings["run_on_start"] = self.run_on_start_var.get()
        self.settings["enable_music"] = self.enable_music_var.get()
        self.settings["enable_backup"] = self.enable_backup_var.get()
        self.settings["fake_bsod_enabled"] = self.fake_bsod_var.get()

        save_settings(self.settings)
        messagebox.showinfo("Settings", "Settings saved!")

if __name__ == "__main__":
    try:
        # Try to use the new comprehensive settings GUI
        from settings_gui import open_settings
        print("Opening comprehensive settings GUI...")
        open_settings()
    except ImportError:
        # Fallback to simple settings
        print("Using simple settings interface...")
        SettingsUI().mainloop()
