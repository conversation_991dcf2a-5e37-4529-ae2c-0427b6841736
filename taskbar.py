import os
import subprocess
import tkinter as tk
from tkinter import PhotoImage
import threading
import webbrowser
from datetime import datetime
import requests

import utils

TASKBAR_HEIGHT = 40
WIDGETS_DIR = "assets/icons"
MAX_APPS = 12

def launch_app(path):
    try:
        if path.lower() == "msedge":
            # Block Microsoft Edge from opening
            return
        if path == "explorer":
            subprocess.Popen("explorer")
        else:
            subprocess.Popen(path)
    except Exception as e:
        print(f"Could not open {path}: {e}")

def get_time_string():
    return datetime.now().strftime("%H:%M")

def get_date_string():
    return datetime.now().strftime("%a/%b/%Y").upper()

def open_settings(settings):
    subprocess.Popen(["python", "settings.py"])

def open_snipping_tool():
    subprocess.Popen(["snippingtool"])

def open_news_weather():
    # Open weather for current city
    try:
        ip_info = requests.get("http://ip-api.com/json").json()
        city = ip_info.get("city", "New York").replace(" ", "-")
        url = f"https://www.accuweather.com/en/search-locations?query={city}"
        webbrowser.get("chrome").open(url)
    except Exception:
        webbrowser.open("https://www.accuweather.com")

def open_cybernews():
    webbrowser.get("chrome").open("https://www.youtube.com/@cybernews")

def init_taskbar(settings):
    root = tk.Tk()
    root.overrideredirect(True)
    root.geometry(f"{root.winfo_screenwidth()}x{TASKBAR_HEIGHT}+0+{root.winfo_screenheight()-TASKBAR_HEIGHT}")
    root.configure(bg=settings.get("taskbar_color", "#57C7E3"))
    root.wm_attributes("-topmost", True)

    left_frame = tk.Frame(root, bg=settings.get("taskbar_color"))
    left_frame.pack(side="left", padx=5)

    # Start Button
    start_img = PhotoImage(file=os.path.join(WIDGETS_DIR, "start.png")).subsample(2, 2)
    start_btn = tk.Button(left_frame, image=start_img, bg=settings.get("taskbar_color"), border=0,
                          command=lambda: launch_app("explorer"))
    start_btn.image = start_img
    start_btn.pack(side="left", padx=5)

    # Weather button (Windows style)
    weather_btn = tk.Button(left_frame, text="🌤️ Weather", bg=settings.get("taskbar_color"), fg="white", border=0,
                            command=open_news_weather)
    weather_btn.pack(side="left", padx=5)

    # Most used apps placeholder (top half)
    # Here we just hardcode some apps - replace with real usage stats later
    most_used_apps = [
        {"name": "Chrome", "path": r"C:\Program Files\Google\Chrome\Application\chrome.exe", "icon": os.path.join(WIDGETS_DIR, "chrome.png")},
        {"name": "VSCode", "path": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe", "icon": os.path.join(WIDGETS_DIR, "vscode.png")},
        {"name": "File Explorer", "path": "explorer", "icon": os.path.join(WIDGETS_DIR, "folder.png")},
        {"name": "Notepad", "path": "notepad", "icon": os.path.join(WIDGETS_DIR, "notepad.png")},
    ]

    for app in most_used_apps:
        try:
            icon = PhotoImage(file=app["icon"]).subsample(2, 2)
        except Exception:
            icon = None
        btn = tk.Button(left_frame, image=icon, bg=settings.get("taskbar_color"), border=0,
                        command=lambda p=app["path"]: launch_app(p))
        btn.image = icon
        btn.pack(side="left", padx=3)

    # Spacer
    spacer = tk.Frame(root, bg=settings.get("taskbar_color"))
    spacer.pack(side="left", expand=True)

    # Right Frame - lesser used apps and utilities
    right_frame = tk.Frame(root, bg=settings.get("taskbar_color"))
    right_frame.pack(side="right", padx=5)

    # Record screen button
    record_img = PhotoImage(file=os.path.join(WIDGETS_DIR, "record.png")).subsample(2, 2)
    record_btn = tk.Button(right_frame, image=record_img, bg=settings.get("taskbar_color"), border=0,
                           command=open_snipping_tool)
    record_btn.image = record_img
    record_btn.pack(side="right", padx=5)

    # News button
    news_btn = tk.Button(right_frame, text="Cybernews", bg=settings.get("taskbar_color"), fg="white", border=0,
                         command=open_cybernews)
    news_btn.pack(side="right", padx=5)

    # Date and Time
    date_lbl = tk.Label(right_frame, text=get_date_string(), bg=settings.get("taskbar_color"), fg="white")
    date_lbl.pack(side="right", padx=10)

    time_lbl = tk.Label(right_frame, text=get_time_string(), bg=settings.get("taskbar_color"), fg="white")
    time_lbl.pack(side="right")

    # Settings cog
    cog_img = PhotoImage(file=os.path.join(WIDGETS_DIR, "settings.png")).subsample(2, 2)
    cog_btn = tk.Button(right_frame, image=cog_img, bg=settings.get("taskbar_color"), border=0,
                        command=lambda: open_settings(settings))
    cog_btn.image = cog_img
    cog_btn.pack(side="right", padx=5)

    def update_time():
        time_lbl.config(text=get_time_string())
        date_lbl.config(text=get_date_string())
        root.after(10000, update_time)

    update_time()
    root.mainloop()
