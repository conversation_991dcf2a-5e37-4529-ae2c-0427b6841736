import os
import subprocess
import tkinter as tk
from tkinter import PhotoImage, ttk
import threading
import webbrowser
from datetime import datetime
import requests
import psutil
import json
import winreg
from collections import defaultdict
import ctypes
from ctypes import wintypes
import win32gui
import win32con
import win32process

import utils

TASKBAR_HEIGHT = 48
WIDGETS_DIR = "assets/icons"
MAX_APPS = 8
USAGE_TRACKING_FILE = "app_usage.json"

# Windows API constants
SW_HIDE = 0
SW_SHOW = 1
SW_MINIMIZE = 6
SW_RESTORE = 9

class WindowManager:
    """Manages window states and taskbar representation"""

    def __init__(self):
        self.open_windows = {}  # hwnd -> window_info
        self.minimized_windows = set()
        self.taskbar_buttons = {}  # hwnd -> button_widget

    def enum_windows_callback(self, hwnd, windows):
        """Callback for enumerating windows"""
        if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
            try:
                # Get window info
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # Skip certain windows
                skip_classes = ['Shell_TrayWnd', 'DV2ControlHost', 'MsgrIMEWindowClass',
                               'SysShadow', 'Button', 'Windows.UI.Core.CoreWindow']

                if class_name not in skip_classes and window_text.strip():
                    # Get process info
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    try:
                        process = psutil.Process(pid)
                        exe_name = process.name()

                        windows[hwnd] = {
                            'title': window_text,
                            'class': class_name,
                            'exe': exe_name,
                            'pid': pid,
                            'minimized': win32gui.IsIconic(hwnd)
                        }
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
            except Exception:
                pass
        return True

    def get_open_windows(self):
        """Get list of open windows"""
        windows = {}
        try:
            win32gui.EnumWindows(self.enum_windows_callback, windows)
        except Exception as e:
            print(f"Error enumerating windows: {e}")
        return windows

    def is_window_minimized(self, hwnd):
        """Check if window is minimized"""
        try:
            return win32gui.IsIconic(hwnd)
        except:
            return False

    def restore_window(self, hwnd):
        """Restore a minimized window"""
        try:
            win32gui.ShowWindow(hwnd, SW_RESTORE)
            win32gui.SetForegroundWindow(hwnd)
        except Exception as e:
            print(f"Error restoring window: {e}")

    def minimize_window(self, hwnd):
        """Minimize a window"""
        try:
            win32gui.ShowWindow(hwnd, SW_MINIMIZE)
        except Exception as e:
            print(f"Error minimizing window: {e}")

# Global window manager
window_manager = WindowManager()

def track_app_usage(app_name):
    """Track app usage for most-used apps feature"""
    try:
        if os.path.exists(USAGE_TRACKING_FILE):
            with open(USAGE_TRACKING_FILE, 'r') as f:
                usage_data = json.load(f)
        else:
            usage_data = {}

        usage_data[app_name] = usage_data.get(app_name, 0) + 1

        with open(USAGE_TRACKING_FILE, 'w') as f:
            json.dump(usage_data, f, indent=2)
    except Exception as e:
        print(f"Error tracking usage for {app_name}: {e}")

def get_most_used_apps():
    """Get most used apps from tracking data"""
    try:
        if os.path.exists(USAGE_TRACKING_FILE):
            with open(USAGE_TRACKING_FILE, 'r') as f:
                usage_data = json.load(f)

            # Sort by usage count and return top apps
            sorted_apps = sorted(usage_data.items(), key=lambda x: x[1], reverse=True)

            # Map to actual app paths
            app_paths = {
                "Chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "VSCode": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv('USERNAME')),
                "File Explorer": "explorer",
                "Notepad": "notepad",
                "Calculator": "calc"
            }

            result = []
            for app_name, count in sorted_apps[:MAX_APPS]:
                if app_name in app_paths:
                    result.append({
                        "name": app_name,
                        "path": app_paths[app_name],
                        "count": count
                    })
            return result
    except Exception as e:
        print(f"Error getting most used apps: {e}")

    # Fallback to default apps
    return [
        {"name": "Chrome", "path": r"C:\Program Files\Google\Chrome\Application\chrome.exe", "count": 0},
        {"name": "VSCode", "path": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv('USERNAME')), "count": 0},
        {"name": "File Explorer", "path": "explorer", "count": 0},
        {"name": "Notepad", "path": "notepad", "count": 0}
    ]

def get_battery_status():
    """Get battery percentage and charging status"""
    try:
        battery = psutil.sensors_battery()
        if battery:
            return {
                "percent": int(battery.percent),
                "plugged": battery.power_plugged,
                "icon": "🔋" if battery.power_plugged else "🪫" if battery.percent < 20 else "🔋"
            }
    except:
        pass
    return {"percent": 100, "plugged": True, "icon": "🔋"}

def launch_app(path):
    try:
        # Block Microsoft Edge
        if "msedge" in path.lower() or "edge" in path.lower():
            print("Microsoft Edge is blocked!")
            return

        # Track usage
        app_name = os.path.basename(path).replace('.exe', '') if path != "explorer" else "File Explorer"
        track_app_usage(app_name)

        if path == "explorer":
            subprocess.Popen("explorer")
        elif path.startswith("http"):
            webbrowser.open(path)
        else:
            subprocess.Popen(path)
    except Exception as e:
        print(f"Could not open {path}: {e}")

def get_time_string():
    return datetime.now().strftime("%H:%M")

def get_date_string():
    return datetime.now().strftime("%a/%b/%Y").upper()

def open_settings(settings=None):
    """Open the settings GUI"""
    try:
        # Try to use the comprehensive settings GUI
        subprocess.Popen(["python", "settings_gui.py"])
    except Exception:
        # Fallback to basic settings
        subprocess.Popen(["python", "settings.py"])

def open_snipping_tool():
    subprocess.Popen(["snippingtool"])

def open_news_weather():
    # Open weather for current city
    try:
        ip_info = requests.get("http://ip-api.com/json").json()
        city = ip_info.get("city", "New York").replace(" ", "-")
        url = f"https://www.accuweather.com/en/search-locations?query={city}"
        webbrowser.get("chrome").open(url)
    except Exception:
        webbrowser.open("https://www.accuweather.com")

def open_cybernews():
    webbrowser.get("chrome").open("https://www.youtube.com/@cybernews")

def hide_windows_taskbar():
    """Hide the Windows taskbar"""
    try:
        import ctypes
        from ctypes import wintypes

        # Find taskbar window
        taskbar = ctypes.windll.user32.FindWindowW("Shell_TrayWnd", None)
        if taskbar:
            # Hide taskbar
            ctypes.windll.user32.ShowWindow(taskbar, 0)
            print("Windows taskbar hidden")
            return True
    except Exception as e:
        print(f"Could not hide Windows taskbar: {e}")
    return False

def show_windows_taskbar():
    """Show the Windows taskbar (for cleanup)"""
    try:
        import ctypes

        taskbar = ctypes.windll.user32.FindWindowW("Shell_TrayWnd", None)
        if taskbar:
            ctypes.windll.user32.ShowWindow(taskbar, 1)
            print("Windows taskbar restored")
            return True
    except Exception as e:
        print(f"Could not restore Windows taskbar: {e}")
    return False

def get_theme_colors(settings):
    """Get colors based on current theme"""
    current_theme = settings.get("current_theme", "Hatsune Miku")
    themes = settings.get("themes", {})

    if current_theme in themes:
        theme = themes[current_theme]
        return {
            "background": theme["background"],
            "buttons": theme["buttons"],
            "text": theme["text"]
        }

    # Fallback to taskbar_colors or defaults
    return {
        "background": settings.get("taskbar_colors", {}).get("background", "#66BFF2"),
        "buttons": settings.get("taskbar_colors", {}).get("buttons", "#4DA6D9"),
        "text": settings.get("taskbar_colors", {}).get("text", "#15BFAE")
    }

def create_window_button(parent, window_info, hwnd, button_color, text_color):
    """Create a Windows-style button for an open window"""
    title = window_info['title']
    exe_name = window_info['exe']

    # Truncate long titles for taskbar
    if len(title) > 15:
        display_title = title[:12] + "..."
    else:
        display_title = title

    # Get app icon (simplified)
    app_icons = {
        'chrome.exe': '🌐',
        'code.exe': '💻',
        'notepad.exe': '📝',
        'explorer.exe': '📁',
        'calc.exe': '🧮',
        'firefox.exe': '🦊',
        'discord.exe': '💬',
        'spotify.exe': '🎵',
        'cmd.exe': '⚫',
        'powershell.exe': '🔵'
    }

    icon = app_icons.get(exe_name.lower(), '📱')

    # Create Windows-style button
    btn = tk.Button(parent, text=f"{icon}\n{display_title}",
                   font=("Segoe UI", 8), bg=button_color, fg=text_color,
                   border=2, relief="raised", width=8, height=2,
                   justify="center",
                   command=lambda: toggle_window(hwnd))

    # Add Windows-style hover effects
    def on_enter(e):
        btn.configure(bg="#505050", relief="raised")

    def on_leave(e):
        btn.configure(bg=button_color, relief="raised")

    def on_click(e):
        btn.configure(relief="sunken")
        btn.after(100, lambda: btn.configure(relief="raised"))

    btn.bind("<Enter>", on_enter)
    btn.bind("<Leave>", on_leave)
    btn.bind("<Button-1>", on_click)

    return btn

def toggle_window(hwnd):
    """Toggle window between minimized and restored"""
    try:
        if window_manager.is_window_minimized(hwnd):
            window_manager.restore_window(hwnd)
        else:
            window_manager.minimize_window(hwnd)
    except Exception as e:
        print(f"Error toggling window: {e}")

def init_taskbar(settings):
    # Hide Windows taskbar first
    hide_windows_taskbar()

    root = tk.Tk()
    root.overrideredirect(True)
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    root.geometry(f"{screen_width}x{TASKBAR_HEIGHT}+0+{screen_height-TASKBAR_HEIGHT}")

    # Get theme colors
    theme_colors = get_theme_colors(settings)
    bg_color = theme_colors["background"]
    button_color = theme_colors["buttons"]
    text_color = theme_colors["text"]

    current_theme = settings.get("current_theme", "Hatsune Miku")
    print(f"Using {current_theme} theme:")
    print(f"  Background: {bg_color}")
    print(f"  Buttons: {button_color}")
    print(f"  Text: {text_color}")

    root.configure(bg=bg_color)
    root.wm_attributes("-topmost", True)

    # Windows-style taskbar appearance
    root.configure(relief="raised", bd=2)

    # Initialize start menu
    import start_menu
    start_menu_instance = start_menu.init_start_menu(root)

    # Initialize music controller
    import music_controls
    music_controller = music_controls.init_music_controller()

    # LEFT SIDE - Start button and core controls
    left_frame = tk.Frame(root, bg=bg_color, relief="flat")
    left_frame.pack(side="left", padx=3)

    # Windows-style Start Button
    start_btn = tk.Button(left_frame, text="⊞", font=("Segoe UI", 16, "bold"),
                         bg=button_color, fg=text_color, border=2, relief="raised",
                         width=3, height=1,
                         activebackground="#505050", activeforeground=text_color,
                         command=start_menu.toggle_start_menu)
    start_btn.pack(side="left", padx=2, pady=3)

    # Add hover effects to start button
    start_btn.bind("<Enter>", lambda e: start_btn.configure(bg="#505050"))
    start_btn.bind("<Leave>", lambda e: start_btn.configure(bg=button_color))

    # Search box (Windows style)
    search_frame = tk.Frame(left_frame, bg=button_color, relief="sunken", bd=1)
    search_frame.pack(side="left", padx=5, pady=5)

    search_entry = tk.Entry(search_frame, font=("Segoe UI", 9), width=20,
                           bg="#FFFFFF", fg="#000000", border=0,
                           relief="flat")
    search_entry.pack(padx=3, pady=3)
    search_entry.insert(0, "🔍 Search...")

    def on_search_focus(event):
        if search_entry.get() == "🔍 Search...":
            search_entry.delete(0, tk.END)

    def on_search_unfocus(event):
        if not search_entry.get():
            search_entry.insert(0, "🔍 Search...")

    search_entry.bind("<FocusIn>", on_search_focus)
    search_entry.bind("<FocusOut>", on_search_unfocus)

    # Music controls
    music_frame = tk.Frame(left_frame, bg=bg_color)
    music_frame.pack(side="left", padx=10)

    # Music control buttons
    play_btn = tk.Button(music_frame, text="▶️", font=("Segoe UI", 10),
                        bg=button_color, fg=text_color, border=1, relief="raised",
                        width=2, command=lambda: music_controls.play_music())
    play_btn.pack(side="left", padx=1)

    pause_btn = tk.Button(music_frame, text="⏸️", font=("Segoe UI", 10),
                         bg=button_color, fg=text_color, border=1, relief="raised",
                         width=2, command=lambda: music_controls.pause_music())
    pause_btn.pack(side="left", padx=1)

    stop_btn = tk.Button(music_frame, text="⏹️", font=("Segoe UI", 10),
                        bg=button_color, fg=text_color, border=1, relief="raised",
                        width=2, command=lambda: music_controls.stop_music())
    stop_btn.pack(side="left", padx=1)

    # WINDOW MANAGEMENT SECTION
    windows_frame = tk.Frame(root, bg=bg_color, relief="sunken", bd=1)
    windows_frame.pack(side="left", padx=10, fill="y")

    # Container for window buttons
    window_buttons_frame = tk.Frame(windows_frame, bg=bg_color)
    window_buttons_frame.pack(side="left", padx=5, pady=2)

    # MIDDLE - Most used apps
    middle_frame = tk.Frame(root, bg=bg_color)
    middle_frame.pack(side="left", padx=20)

    most_used_apps = get_most_used_apps()

    # App icons with text labels (since we don't have icon files)
    app_icons = {
        "Chrome": "🌐",
        "VSCode": "💻",
        "File Explorer": "📁",
        "Notepad": "📝",
        "Calculator": "🧮"
    }

    for app in most_used_apps:
        app_name = app["name"]
        icon_text = app_icons.get(app_name, "📱")

        app_btn = tk.Button(middle_frame, text=icon_text, font=("Segoe UI", 14),
                           bg=button_color, fg=text_color, border=0, relief="flat",
                           command=lambda p=app["path"]: launch_app(p))
        app_btn.pack(side="left", padx=3)

        # Tooltip showing app name and usage count
        def create_tooltip(widget, text):
            def on_enter(event):
                tooltip = tk.Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
                label = tk.Label(tooltip, text=text, bg="yellow", fg="black")
                label.pack()
                widget.tooltip = tooltip

            def on_leave(event):
                if hasattr(widget, 'tooltip'):
                    widget.tooltip.destroy()
                    del widget.tooltip

            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)

        create_tooltip(app_btn, f"{app_name}\nUsed: {app.get('count', 0)} times")

    # Spacer to push right elements to the right
    spacer = tk.Frame(root, bg=bg_color)
    spacer.pack(side="left", expand=True, fill="x")

    # RIGHT SIDE - Chrome-style system tray
    right_frame = tk.Frame(root, bg=bg_color)
    right_frame.pack(side="right", padx=10)

    # Record screen button
    record_btn = tk.Button(right_frame, text="🎥", font=("Segoe UI", 12),
                          bg=button_color, fg=text_color, border=0, relief="flat",
                          command=open_snipping_tool)
    record_btn.pack(side="right", padx=2)

    # News button
    news_btn = tk.Button(right_frame, text="📰", font=("Segoe UI", 12),
                        bg=button_color, fg=text_color, border=0, relief="flat",
                        command=open_cybernews)
    news_btn.pack(side="right", padx=2)

    # Settings cog
    settings_btn = tk.Button(right_frame, text="⚙️", font=("Segoe UI", 12),
                            bg=button_color, fg=text_color, border=0, relief="flat",
                            command=lambda: open_settings(settings))
    settings_btn.pack(side="right", padx=2)

    # Windows-style system info panel (Battery, Time, Date grouped)
    system_panel = tk.Frame(right_frame, bg=button_color, relief="raised", bd=2)
    system_panel.pack(side="right", padx=5, pady=2)

    # Battery status with Windows styling
    battery_info = get_battery_status()
    battery_lbl = tk.Label(system_panel, text=f"{battery_info['icon']} {battery_info['percent']}%",
                          bg=button_color, fg=text_color, font=("Segoe UI", 9))
    battery_lbl.pack(side="top", padx=8, pady=2)

    # Time (Windows 11 style - larger and prominent)
    time_lbl = tk.Label(system_panel, text=get_time_string(),
                       bg=button_color, fg=text_color, font=("Segoe UI", 12, "bold"))
    time_lbl.pack(side="top", padx=8, pady=1)

    # Date (Windows style - smaller but readable)
    date_lbl = tk.Label(system_panel, text=get_date_string(),
                       bg=button_color, fg=text_color, font=("Segoe UI", 9))
    date_lbl.pack(side="top", padx=8, pady=2)

    def update_window_buttons():
        """Update the window buttons to show current open windows (HIDE MINIMIZED)"""
        try:
            # Clear existing buttons
            for widget in window_buttons_frame.winfo_children():
                widget.destroy()

            # Get current windows
            open_windows = window_manager.get_open_windows()

            # Create buttons ONLY for non-minimized windows
            button_count = 0
            max_buttons = 6  # Limit number of window buttons for space

            for hwnd, window_info in open_windows.items():
                if button_count >= max_buttons:
                    break

                # Skip system windows
                if window_info['exe'].lower() in ['dwm.exe', 'winlogon.exe', 'csrss.exe', 'explorer.exe']:
                    continue

                # ONLY show non-minimized windows (this is the key fix!)
                if not window_info['minimized']:
                    # Create window button for visible windows only
                    btn = create_window_button(window_buttons_frame, window_info, hwnd,
                                             button_color, text_color)
                    btn.pack(side="left", padx=1, pady=2)
                    button_count += 1

        except Exception as e:
            print(f"Error updating window buttons: {e}")

    def update_system_info():
        """Update time, date, battery info, and window buttons"""
        try:
            # Update time and date
            time_lbl.config(text=get_time_string())
            date_lbl.config(text=get_date_string())

            # Update battery
            battery_info = get_battery_status()
            battery_lbl.config(text=f"{battery_info['icon']} {battery_info['percent']}%")

            # Update window buttons
            update_window_buttons()

            # Schedule next update
            root.after(3000, update_system_info)  # Update every 3 seconds for responsive window management
        except Exception as e:
            print(f"Error updating system info: {e}")
            root.after(10000, update_system_info)  # Retry in 10 seconds

    # Start the update loops
    update_system_info()

    # Initial window button update
    root.after(1000, update_window_buttons)  # Initial load after 1 second

    # Handle cleanup when window is closed
    def on_closing():
        """Cleanup function to restore Windows taskbar"""
        print("Cleaning up WindowsCustom taskbar...")
        show_windows_taskbar()  # Restore Windows taskbar
        root.quit()  # Exit mainloop
        root.destroy()  # Destroy window

    # Multiple ways to ensure cleanup happens
    root.protocol("WM_DELETE_WINDOW", on_closing)

    # Handle system shutdown/logout
    def on_destroy(event=None):
        on_closing()

    root.bind("<Destroy>", on_destroy)

    # Add Windows-style taskbar behavior
    def on_taskbar_click(event):
        """Handle clicks on empty taskbar space"""
        # Could implement desktop show/hide here
        pass

    root.bind("<Button-1>", on_taskbar_click)

    try:
        print("Custom Windows-themed taskbar started")
        print(f"- Theme: {current_theme}")
        print("- Window management: Minimized apps hidden from taskbar")
        print("- Real-time window state updates")
        print("- Press Ctrl+C or close to restore Windows taskbar")
        root.mainloop()
    except KeyboardInterrupt:
        print("Interrupted by user")
        on_closing()
    except Exception as e:
        print(f"Taskbar error: {e}")
        on_closing()
    finally:
        # Ensure Windows taskbar is always restored
        show_windows_taskbar()
