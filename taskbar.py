import os
import subprocess
import tkinter as tk
from tkinter import PhotoImage, ttk
import threading
import webbrowser
from datetime import datetime
import requests
import psutil
import json
import winreg
from collections import defaultdict

import utils

TASKBAR_HEIGHT = 50
WIDGETS_DIR = "assets/icons"
MAX_APPS = 8
USAGE_TRACKING_FILE = "app_usage.json"

def track_app_usage(app_name):
    """Track app usage for most-used apps feature"""
    try:
        if os.path.exists(USAGE_TRACKING_FILE):
            with open(USAGE_TRACKING_FILE, 'r') as f:
                usage_data = json.load(f)
        else:
            usage_data = {}

        usage_data[app_name] = usage_data.get(app_name, 0) + 1

        with open(USAGE_TRACKING_FILE, 'w') as f:
            json.dump(usage_data, f, indent=2)
    except Exception as e:
        print(f"Error tracking usage for {app_name}: {e}")

def get_most_used_apps():
    """Get most used apps from tracking data"""
    try:
        if os.path.exists(USAGE_TRACKING_FILE):
            with open(USAGE_TRACKING_FILE, 'r') as f:
                usage_data = json.load(f)

            # Sort by usage count and return top apps
            sorted_apps = sorted(usage_data.items(), key=lambda x: x[1], reverse=True)

            # Map to actual app paths
            app_paths = {
                "Chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "VSCode": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv('USERNAME')),
                "File Explorer": "explorer",
                "Notepad": "notepad",
                "Calculator": "calc"
            }

            result = []
            for app_name, count in sorted_apps[:MAX_APPS]:
                if app_name in app_paths:
                    result.append({
                        "name": app_name,
                        "path": app_paths[app_name],
                        "count": count
                    })
            return result
    except Exception as e:
        print(f"Error getting most used apps: {e}")

    # Fallback to default apps
    return [
        {"name": "Chrome", "path": r"C:\Program Files\Google\Chrome\Application\chrome.exe", "count": 0},
        {"name": "VSCode", "path": r"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe".format(os.getenv('USERNAME')), "count": 0},
        {"name": "File Explorer", "path": "explorer", "count": 0},
        {"name": "Notepad", "path": "notepad", "count": 0}
    ]

def get_battery_status():
    """Get battery percentage and charging status"""
    try:
        battery = psutil.sensors_battery()
        if battery:
            return {
                "percent": int(battery.percent),
                "plugged": battery.power_plugged,
                "icon": "🔋" if battery.power_plugged else "🪫" if battery.percent < 20 else "🔋"
            }
    except:
        pass
    return {"percent": 100, "plugged": True, "icon": "🔋"}

def launch_app(path):
    try:
        # Block Microsoft Edge
        if "msedge" in path.lower() or "edge" in path.lower():
            print("Microsoft Edge is blocked!")
            return

        # Track usage
        app_name = os.path.basename(path).replace('.exe', '') if path != "explorer" else "File Explorer"
        track_app_usage(app_name)

        if path == "explorer":
            subprocess.Popen("explorer")
        elif path.startswith("http"):
            webbrowser.open(path)
        else:
            subprocess.Popen(path)
    except Exception as e:
        print(f"Could not open {path}: {e}")

def get_time_string():
    return datetime.now().strftime("%H:%M")

def get_date_string():
    return datetime.now().strftime("%a/%b/%Y").upper()

def open_settings(settings):
    subprocess.Popen(["python", "settings.py"])

def open_snipping_tool():
    subprocess.Popen(["snippingtool"])

def open_news_weather():
    # Open weather for current city
    try:
        ip_info = requests.get("http://ip-api.com/json").json()
        city = ip_info.get("city", "New York").replace(" ", "-")
        url = f"https://www.accuweather.com/en/search-locations?query={city}"
        webbrowser.get("chrome").open(url)
    except Exception:
        webbrowser.open("https://www.accuweather.com")

def open_cybernews():
    webbrowser.get("chrome").open("https://www.youtube.com/@cybernews")

def hide_windows_taskbar():
    """Hide the Windows taskbar"""
    try:
        import ctypes
        from ctypes import wintypes

        # Find taskbar window
        taskbar = ctypes.windll.user32.FindWindowW("Shell_TrayWnd", None)
        if taskbar:
            # Hide taskbar
            ctypes.windll.user32.ShowWindow(taskbar, 0)
            print("Windows taskbar hidden")
    except Exception as e:
        print(f"Could not hide Windows taskbar: {e}")

def show_windows_taskbar():
    """Show the Windows taskbar (for cleanup)"""
    try:
        import ctypes

        taskbar = ctypes.windll.user32.FindWindowW("Shell_TrayWnd", None)
        if taskbar:
            ctypes.windll.user32.ShowWindow(taskbar, 1)
            print("Windows taskbar restored")
    except Exception as e:
        print(f"Could not restore Windows taskbar: {e}")

def init_taskbar(settings):
    # Hide Windows taskbar first
    hide_windows_taskbar()

    root = tk.Tk()
    root.overrideredirect(True)
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    root.geometry(f"{screen_width}x{TASKBAR_HEIGHT}+0+{screen_height-TASKBAR_HEIGHT}")

    # Get colors from settings
    bg_color = settings.get("taskbar_colors", {}).get("background", "#202020")
    button_color = settings.get("taskbar_colors", {}).get("buttons", "#404040")
    text_color = settings.get("taskbar_colors", {}).get("text", "#FFFFFF")

    root.configure(bg=bg_color)
    root.wm_attributes("-topmost", True)

    # LEFT SIDE - Start button and weather
    left_frame = tk.Frame(root, bg=bg_color)
    left_frame.pack(side="left", padx=5)

    # Start Button (moved to far left)
    start_btn = tk.Button(left_frame, text="⊞", font=("Segoe UI", 16),
                         bg=button_color, fg=text_color, border=0, relief="flat",
                         command=lambda: launch_app("explorer"))
    start_btn.pack(side="left", padx=2)

    # Weather button (next to start)
    weather_btn = tk.Button(left_frame, text="🌤️", font=("Segoe UI", 12),
                           bg=button_color, fg=text_color, border=0, relief="flat",
                           command=open_news_weather)
    weather_btn.pack(side="left", padx=2)

    # MIDDLE - Most used apps
    middle_frame = tk.Frame(root, bg=bg_color)
    middle_frame.pack(side="left", padx=20)

    most_used_apps = get_most_used_apps()

    # App icons with text labels (since we don't have icon files)
    app_icons = {
        "Chrome": "🌐",
        "VSCode": "💻",
        "File Explorer": "📁",
        "Notepad": "📝",
        "Calculator": "🧮"
    }

    for app in most_used_apps:
        app_name = app["name"]
        icon_text = app_icons.get(app_name, "📱")

        app_btn = tk.Button(middle_frame, text=icon_text, font=("Segoe UI", 14),
                           bg=button_color, fg=text_color, border=0, relief="flat",
                           command=lambda p=app["path"]: launch_app(p))
        app_btn.pack(side="left", padx=3)

        # Tooltip showing app name and usage count
        def create_tooltip(widget, text):
            def on_enter(event):
                tooltip = tk.Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
                label = tk.Label(tooltip, text=text, bg="yellow", fg="black")
                label.pack()
                widget.tooltip = tooltip

            def on_leave(event):
                if hasattr(widget, 'tooltip'):
                    widget.tooltip.destroy()
                    del widget.tooltip

            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)

        create_tooltip(app_btn, f"{app_name}\nUsed: {app.get('count', 0)} times")

    # Spacer to push right elements to the right
    spacer = tk.Frame(root, bg=bg_color)
    spacer.pack(side="left", expand=True, fill="x")

    # RIGHT SIDE - Chrome-style system tray
    right_frame = tk.Frame(root, bg=bg_color)
    right_frame.pack(side="right", padx=10)

    # Record screen button
    record_btn = tk.Button(right_frame, text="🎥", font=("Segoe UI", 12),
                          bg=button_color, fg=text_color, border=0, relief="flat",
                          command=open_snipping_tool)
    record_btn.pack(side="right", padx=2)

    # News button
    news_btn = tk.Button(right_frame, text="📰", font=("Segoe UI", 12),
                        bg=button_color, fg=text_color, border=0, relief="flat",
                        command=open_cybernews)
    news_btn.pack(side="right", padx=2)

    # Settings cog
    settings_btn = tk.Button(right_frame, text="⚙️", font=("Segoe UI", 12),
                            bg=button_color, fg=text_color, border=0, relief="flat",
                            command=lambda: open_settings(settings))
    settings_btn.pack(side="right", padx=2)

    # Chrome-style system info panel (Battery, Time, Date grouped)
    system_panel = tk.Frame(right_frame, bg=button_color, relief="raised", bd=1)
    system_panel.pack(side="right", padx=5)

    # Battery status
    battery_info = get_battery_status()
    battery_lbl = tk.Label(system_panel, text=f"{battery_info['icon']} {battery_info['percent']}%",
                          bg=button_color, fg=text_color, font=("Segoe UI", 9))
    battery_lbl.pack(side="top", padx=5, pady=1)

    # Time (larger)
    time_lbl = tk.Label(system_panel, text=get_time_string(),
                       bg=button_color, fg=text_color, font=("Segoe UI", 11, "bold"))
    time_lbl.pack(side="top", padx=5)

    # Date (smaller)
    date_lbl = tk.Label(system_panel, text=get_date_string(),
                       bg=button_color, fg=text_color, font=("Segoe UI", 8))
    date_lbl.pack(side="top", padx=5, pady=1)

    def update_system_info():
        """Update time, date, and battery info"""
        try:
            # Update time and date
            time_lbl.config(text=get_time_string())
            date_lbl.config(text=get_date_string())

            # Update battery
            battery_info = get_battery_status()
            battery_lbl.config(text=f"{battery_info['icon']} {battery_info['percent']}%")

            # Schedule next update
            root.after(5000, update_system_info)  # Update every 5 seconds
        except Exception as e:
            print(f"Error updating system info: {e}")
            root.after(10000, update_system_info)  # Retry in 10 seconds

    # Start the update loop
    update_system_info()

    # Handle cleanup when window is closed
    def on_closing():
        show_windows_taskbar()  # Restore Windows taskbar
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        on_closing()
