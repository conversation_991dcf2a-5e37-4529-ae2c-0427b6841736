#!/usr/bin/env python3
"""
Windows Taskbar Editor for WindowsCustom
Modifies the existing Windows taskbar instead of replacing it
"""

import ctypes
from ctypes import wintypes
import win32gui
import win32con
import tkinter as tk
from tkinter import ttk
import threading
import time
import json
import os

class TaskbarEditor:
    def __init__(self):
        self.taskbar_hwnd = None
        self.overlay_widgets = []
        self.music_controls = None
        self.system_tray_overlay = None
        self.running = False
        
    def find_taskbar(self):
        """Find the Windows taskbar window"""
        try:
            self.taskbar_hwnd = win32gui.FindWindow("Shell_TrayWnd", None)
            if self.taskbar_hwnd:
                print(f"✅ Found Windows taskbar: {self.taskbar_hwnd}")
                return True
            else:
                print("❌ Could not find Windows taskbar")
                return False
        except Exception as e:
            print(f"❌ Error finding taskbar: {e}")
            return False
    
    def get_taskbar_info(self):
        """Get taskbar position and size"""
        if not self.taskbar_hwnd:
            return None
        
        try:
            rect = win32gui.GetWindowRect(self.taskbar_hwnd)
            return {
                'left': rect[0],
                'top': rect[1], 
                'right': rect[2],
                'bottom': rect[3],
                'width': rect[2] - rect[0],
                'height': rect[3] - rect[1]
            }
        except Exception as e:
            print(f"Error getting taskbar info: {e}")
            return None
    
    def load_theme_colors(self):
        """Load theme colors from settings"""
        try:
            if os.path.exists("settings.json"):
                with open("settings.json", "r") as f:
                    settings = json.load(f)
                
                current_theme = settings.get("current_theme", "Hatsune Miku")
                themes = settings.get("themes", {})
                
                if current_theme in themes:
                    theme = themes[current_theme]
                    return {
                        "background": theme["background"],
                        "buttons": theme["buttons"],
                        "text": theme["text"]
                    }
        except Exception as e:
            print(f"Error loading theme: {e}")
        
        # Default Hatsune Miku theme
        return {
            "background": "#66BFF2",
            "buttons": "#39C5BB", 
            "text": "#1E3A8A"
        }
    
    def create_music_controls_overlay(self):
        """Create music controls overlay on taskbar"""
        taskbar_info = self.get_taskbar_info()
        if not taskbar_info:
            return
        
        colors = self.load_theme_colors()
        
        # Create overlay window
        self.music_controls = tk.Toplevel()
        self.music_controls.title("")
        self.music_controls.overrideredirect(True)
        self.music_controls.configure(bg=colors["background"])
        self.music_controls.wm_attributes("-topmost", True)
        
        # Position on left side of taskbar
        control_width = 120
        control_height = taskbar_info['height'] - 4
        x = taskbar_info['left'] + 200  # After start button and search
        y = taskbar_info['top'] + 2
        
        self.music_controls.geometry(f"{control_width}x{control_height}+{x}+{y}")
        
        # Create music control buttons
        controls_frame = tk.Frame(self.music_controls, bg=colors["background"])
        controls_frame.pack(fill="both", expand=True, padx=2, pady=2)
        
        # Play button
        play_btn = tk.Button(controls_frame, text="▶️", font=("Segoe UI", 8),
                            bg=colors["buttons"], fg=colors["text"],
                            border=1, relief="raised", width=3,
                            command=self.play_music)
        play_btn.pack(side="left", padx=1)
        
        # Pause button  
        pause_btn = tk.Button(controls_frame, text="⏸️", font=("Segoe UI", 8),
                             bg=colors["buttons"], fg=colors["text"],
                             border=1, relief="raised", width=3,
                             command=self.pause_music)
        pause_btn.pack(side="left", padx=1)
        
        # Stop button
        stop_btn = tk.Button(controls_frame, text="⏹️", font=("Segoe UI", 8),
                            bg=colors["buttons"], fg=colors["text"],
                            border=1, relief="raised", width=3,
                            command=self.stop_music)
        stop_btn.pack(side="left", padx=1)
        
        print("✅ Music controls overlay created")
    
    def create_system_tray_overlay(self):
        """Create enhanced system tray overlay"""
        taskbar_info = self.get_taskbar_info()
        if not taskbar_info:
            return
        
        colors = self.load_theme_colors()
        
        # Create overlay window
        self.system_tray_overlay = tk.Toplevel()
        self.system_tray_overlay.title("")
        self.system_tray_overlay.overrideredirect(True)
        self.system_tray_overlay.configure(bg=colors["background"])
        self.system_tray_overlay.wm_attributes("-topmost", True)
        
        # Position on right side of taskbar
        overlay_width = 200
        overlay_height = taskbar_info['height'] - 4
        x = taskbar_info['right'] - overlay_width - 10
        y = taskbar_info['top'] + 2
        
        self.system_tray_overlay.geometry(f"{overlay_width}x{overlay_height}+{x}+{y}")
        
        # Create enhanced system tray
        tray_frame = tk.Frame(self.system_tray_overlay, bg=colors["buttons"], 
                             relief="raised", bd=1)
        tray_frame.pack(fill="both", expand=True, padx=2, pady=2)
        
        # Settings button
        settings_btn = tk.Button(tray_frame, text="⚙️", font=("Segoe UI", 8),
                                bg=colors["buttons"], fg=colors["text"],
                                border=1, relief="raised", width=3,
                                command=self.open_settings)
        settings_btn.pack(side="left", padx=1)
        
        # Theme button
        theme_btn = tk.Button(tray_frame, text="🎨", font=("Segoe UI", 8),
                             bg=colors["buttons"], fg=colors["text"],
                             border=1, relief="raised", width=3,
                             command=self.open_theme_selector)
        theme_btn.pack(side="left", padx=1)
        
        # Battery and time info
        info_label = tk.Label(tray_frame, text="🔋85% 12:34", 
                             font=("Segoe UI", 8),
                             bg=colors["buttons"], fg=colors["text"])
        info_label.pack(side="right", padx=5)
        
        # Update info periodically
        self.update_system_info(info_label)
        
        print("✅ System tray overlay created")
    
    def update_system_info(self, label):
        """Update system information display"""
        try:
            import psutil
            from datetime import datetime
            
            # Get battery info
            battery = psutil.sensors_battery()
            if battery:
                battery_text = f"🔋{int(battery.percent)}%"
            else:
                battery_text = "🔋--"
            
            # Get time
            time_text = datetime.now().strftime("%H:%M")
            
            # Update label
            label.config(text=f"{battery_text} {time_text}")
            
            # Schedule next update
            if self.running:
                label.after(30000, lambda: self.update_system_info(label))  # Update every 30 seconds
                
        except Exception as e:
            print(f"Error updating system info: {e}")
    
    def play_music(self):
        """Play music"""
        try:
            import music_controls
            music_controls.play_music()
            print("🎵 Music play command sent")
        except Exception as e:
            print(f"Music play error: {e}")
    
    def pause_music(self):
        """Pause music"""
        try:
            import music_controls
            music_controls.pause_music()
            print("⏸️ Music pause command sent")
        except Exception as e:
            print(f"Music pause error: {e}")
    
    def stop_music(self):
        """Stop music"""
        try:
            import music_controls
            music_controls.stop_music()
            print("⏹️ Music stop command sent")
        except Exception as e:
            print(f"Music stop error: {e}")
    
    def open_settings(self):
        """Open WindowsCustom settings"""
        try:
            import subprocess
            subprocess.Popen(["python", "settings_gui.py"])
            print("⚙️ Opening settings")
        except Exception as e:
            print(f"Settings error: {e}")
    
    def open_theme_selector(self):
        """Open quick theme selector"""
        # Create quick theme popup
        theme_popup = tk.Toplevel()
        theme_popup.title("Quick Theme")
        theme_popup.geometry("200x120")
        theme_popup.configure(bg="#2D2D2D")
        theme_popup.wm_attributes("-topmost", True)
        
        tk.Label(theme_popup, text="Quick Theme", 
                font=("Segoe UI", 10, "bold"),
                bg="#2D2D2D", fg="#FFFFFF").pack(pady=5)
        
        # Theme buttons
        tk.Button(theme_popup, text="🔴 Red", bg="#FF6B6B", fg="white",
                 command=lambda: self.apply_theme("Red")).pack(pady=2)
        
        tk.Button(theme_popup, text="💙 Miku", bg="#66BFF2", fg="#1E3A8A",
                 command=lambda: self.apply_theme("Hatsune Miku")).pack(pady=2)
        
        tk.Button(theme_popup, text="⚫ Dark", bg="#1F1F1F", fg="white",
                 command=lambda: self.apply_theme("Windows Dark")).pack(pady=2)
    
    def apply_theme(self, theme_name):
        """Apply a theme and restart overlays"""
        try:
            # Update settings
            if os.path.exists("settings.json"):
                with open("settings.json", "r") as f:
                    settings = json.load(f)
                
                settings["current_theme"] = theme_name
                
                with open("settings.json", "w") as f:
                    json.dump(settings, f, indent=2)
                
                print(f"🎨 Applied {theme_name} theme")
                
                # Restart overlays with new theme
                self.restart_overlays()
                
        except Exception as e:
            print(f"Theme apply error: {e}")
    
    def restart_overlays(self):
        """Restart overlays with new theme"""
        self.stop_overlays()
        time.sleep(0.5)
        self.start_overlays()
    
    def start_overlays(self):
        """Start all taskbar overlays"""
        if not self.find_taskbar():
            print("❌ Cannot start overlays - taskbar not found")
            return False
        
        self.running = True
        
        try:
            self.create_music_controls_overlay()
            self.create_system_tray_overlay()
            print("✅ Taskbar overlays started")
            return True
        except Exception as e:
            print(f"❌ Error starting overlays: {e}")
            return False
    
    def stop_overlays(self):
        """Stop all taskbar overlays"""
        self.running = False
        
        if self.music_controls:
            self.music_controls.destroy()
            self.music_controls = None
        
        if self.system_tray_overlay:
            self.system_tray_overlay.destroy()
            self.system_tray_overlay = None
        
        print("🛑 Taskbar overlays stopped")
    
    def run(self):
        """Run the taskbar editor"""
        print("WindowsCustom Taskbar Editor")
        print("=" * 30)
        print("Enhancing existing Windows taskbar...")
        
        if self.start_overlays():
            print("\n✅ Taskbar enhancement active!")
            print("🎵 Music controls added to taskbar")
            print("🎨 Enhanced system tray with themes")
            print("⚙️ Quick access to WindowsCustom settings")
            print("\nPress Ctrl+C to stop")
            
            try:
                # Keep running
                root = tk.Tk()
                root.withdraw()  # Hide root window
                root.mainloop()
            except KeyboardInterrupt:
                print("\n🛑 Stopping taskbar editor...")
            finally:
                self.stop_overlays()
        else:
            print("❌ Failed to enhance taskbar")

def main():
    """Main function"""
    editor = TaskbarEditor()
    editor.run()

if __name__ == "__main__":
    main()
