#!/usr/bin/env python3
"""
Test window management functionality
"""

import sys
import time

def test_imports():
    """Test Windows API imports"""
    print("Testing Windows API imports...")
    
    try:
        import win32gui
        import win32con
        import win32process
        print("✓ pywin32 modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import pywin32: {e}")
        print("Install with: pip install pywin32")
        return False

def test_window_enumeration():
    """Test window enumeration"""
    print("\nTesting window enumeration...")
    
    try:
        import win32gui
        import win32process
        import psutil
        
        def enum_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
                try:
                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    
                    try:
                        process = psutil.Process(pid)
                        exe_name = process.name()
                        is_minimized = win32gui.IsIconic(hwnd)
                        
                        windows.append({
                            'hwnd': hwnd,
                            'title': title,
                            'class': class_name,
                            'exe': exe_name,
                            'minimized': is_minimized
                        })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                except Exception:
                    pass
            return True
        
        windows = []
        win32gui.EnumWindows(enum_callback, windows)
        
        print(f"✓ Found {len(windows)} windows")
        
        # Show some examples
        print("\nSample windows:")
        for i, window in enumerate(windows[:5]):
            status = "MINIMIZED" if window['minimized'] else "VISIBLE"
            print(f"  {i+1}. {window['title'][:30]} ({window['exe']}) - {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ Window enumeration failed: {e}")
        return False

def test_taskbar_module():
    """Test our enhanced taskbar module"""
    print("\nTesting enhanced taskbar module...")
    
    try:
        import taskbar
        
        # Test window manager
        wm = taskbar.WindowManager()
        print("✓ WindowManager created")
        
        # Test getting open windows
        windows = wm.get_open_windows()
        print(f"✓ Found {len(windows)} open windows")
        
        # Test other functions
        battery = taskbar.get_battery_status()
        print(f"✓ Battery status: {battery['percent']}% ({'plugged' if battery['plugged'] else 'unplugged'})")
        
        time_str = taskbar.get_time_string()
        date_str = taskbar.get_date_string()
        print(f"✓ Time: {time_str}, Date: {date_str}")
        
        return True
        
    except Exception as e:
        print(f"✗ Taskbar module test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Window Management Test Suite")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_window_enumeration,
        test_taskbar_module
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✓ All tests passed! Window management should work.")
        print("\nYou can now run the enhanced taskbar with:")
        print("python launch_windowscustom.py")
    else:
        print("✗ Some tests failed. Check the errors above.")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
