#!/usr/bin/env python3
"""
WindowsCustom Color Reference
Visual demonstration of the exact theme colors
"""

import tkinter as tk
import json
import os

class ColorReference:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("WindowsCustom Color Reference - BY Aladdin <PERSON>ewa")
        self.root.geometry("800x600")
        self.root.configure(bg="#2D2D2D")
        
        self.load_themes()
        self.create_widgets()
        
    def load_themes(self):
        """Load theme data"""
        if os.path.exists("settings.json"):
            with open("settings.json", "r") as f:
                settings = json.load(f)
                self.themes = settings.get("themes", {})
        else:
            self.themes = {}
    
    def create_widgets(self):
        """Create the color reference interface"""
        # Title
        title = tk.Label(self.root, text="WindowsCustom Color Reference", 
                        font=("Segoe UI", 20, "bold"), 
                        bg="#2D2D2D", fg="#FFFFFF")
        title.pack(pady=20)
        
        subtitle = tk.Label(self.root, text="BY Aladdin <PERSON>ewa", 
                           font=("Segoe UI", 12), 
                           bg="#2D2D2D", fg="#66BFF2")
        subtitle.pack(pady=5)
        
        # Create theme demonstrations
        for theme_name, colors in self.themes.items():
            self.create_theme_demo(theme_name, colors)
    
    def create_theme_demo(self, theme_name, colors):
        """Create a visual demo for each theme"""
        # Theme container
        theme_frame = tk.Frame(self.root, bg="#2D2D2D", relief="raised", bd=2)
        theme_frame.pack(fill="x", padx=20, pady=10)
        
        # Theme title
        title_label = tk.Label(theme_frame, text=f"{theme_name} Theme", 
                              font=("Segoe UI", 16, "bold"),
                              bg="#2D2D2D", fg="#FFFFFF")
        title_label.pack(pady=10)
        
        # Color demonstration
        demo_frame = tk.Frame(theme_frame, bg=colors["background"], 
                             height=80, relief="raised", bd=2)
        demo_frame.pack(fill="x", padx=10, pady=5)
        demo_frame.pack_propagate(False)
        
        # Sample button (represents taskbar buttons)
        sample_btn = tk.Button(demo_frame, text="Sample Button", 
                              bg=colors["buttons"], fg=colors["text"],
                              font=("Segoe UI", 10, "bold"), relief="raised")
        sample_btn.pack(side="left", padx=10, pady=10)
        
        # Sample text (represents taskbar text)
        sample_text = tk.Label(demo_frame, text="Sample Text | 12:34 | 🔋 85%", 
                              bg=colors["background"], fg=colors["text"],
                              font=("Segoe UI", 10))
        sample_text.pack(side="right", padx=10, pady=10)
        
        # Color codes
        codes_frame = tk.Frame(theme_frame, bg="#2D2D2D")
        codes_frame.pack(fill="x", padx=10, pady=5)
        
        # Background color
        bg_frame = tk.Frame(codes_frame, bg=colors["background"], width=50, height=30)
        bg_frame.pack(side="left", padx=5)
        bg_frame.pack_propagate(False)
        
        bg_label = tk.Label(codes_frame, text=f"Background: {colors['background']}", 
                           bg="#2D2D2D", fg="#FFFFFF", font=("Consolas", 9))
        bg_label.pack(side="left", padx=5)
        
        # Button color
        btn_frame = tk.Frame(codes_frame, bg=colors["buttons"], width=50, height=30)
        btn_frame.pack(side="left", padx=5)
        btn_frame.pack_propagate(False)
        
        btn_label = tk.Label(codes_frame, text=f"Buttons: {colors['buttons']}", 
                            bg="#2D2D2D", fg="#FFFFFF", font=("Consolas", 9))
        btn_label.pack(side="left", padx=5)
        
        # Text color
        text_frame = tk.Frame(codes_frame, bg=colors["text"], width=50, height=30)
        text_frame.pack(side="left", padx=5)
        text_frame.pack_propagate(False)
        
        text_label = tk.Label(codes_frame, text=f"Text: {colors['text']}", 
                             bg="#2D2D2D", fg="#FFFFFF", font=("Consolas", 9))
        text_label.pack(side="left", padx=5)
        
        # Special note for Hatsune Miku
        if theme_name == "Hatsune Miku":
            note_label = tk.Label(theme_frame, 
                                 text="✨ Buttons: Hatsune Miku shirt color | Text: Dark blue", 
                                 bg="#2D2D2D", fg="#39C5BB", font=("Segoe UI", 9, "italic"))
            note_label.pack(pady=5)
    
    def run(self):
        """Run the color reference"""
        self.root.mainloop()

def show_color_reference():
    """Show the color reference window"""
    color_ref = ColorReference()
    color_ref.run()

if __name__ == "__main__":
    show_color_reference()
