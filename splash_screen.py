import tkinter as tk
from tkinter import PhotoImage
import threading
import time
import os

class SplashScreen:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("WindowsCustom")
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Make fullscreen
        self.root.geometry(f"{screen_width}x{screen_height}+0+0")
        self.root.configure(bg='#808080')  # Grey background
        self.root.overrideredirect(True)  # Remove window decorations
        self.root.attributes('-topmost', True)  # Always on top
        
    def create_widgets(self):
        # Main container
        main_frame = tk.Frame(self.root, bg='#808080')
        main_frame.pack(expand=True, fill='both')
        
        # Windows logo placeholder (you'll need to add actual Windows logo PNG)
        try:
            # Try to load Windows logo if available
            if os.path.exists("assets/windows_logo.png"):
                logo_img = PhotoImage(file="assets/windows_logo.png")
                logo_label = tk.Label(main_frame, image=logo_img, bg='#808080')
                logo_label.image = logo_img  # Keep reference
            else:
                # Fallback text logo
                logo_label = tk.Label(main_frame, text="⊞", font=("Segoe UI", 120), 
                                    fg='white', bg='#808080')
        except:
            # Fallback text logo
            logo_label = tk.Label(main_frame, text="⊞", font=("Segoe UI", 120), 
                                fg='white', bg='#808080')
        
        logo_label.pack(pady=(200, 50))
        
        # Title text
        title_label = tk.Label(main_frame, text="WindowsCustom BY Aladdin Shenewa", 
                              font=("Segoe UI", 24, "bold"), fg='white', bg='#808080')
        title_label.pack(pady=20)
        
        # Loading animation
        self.loading_label = tk.Label(main_frame, text="Loading...", 
                                     font=("Segoe UI", 16), fg='white', bg='#808080')
        self.loading_label.pack(pady=50)
        
        # Progress bar simulation
        self.progress_frame = tk.Frame(main_frame, bg='#808080')
        self.progress_frame.pack(pady=20)
        
        self.progress_bar = tk.Frame(self.progress_frame, bg='white', height=4, width=0)
        self.progress_bar.pack()
        
        # Start loading animation
        self.animate_loading()
        
    def animate_loading(self):
        """Animate the loading text and progress bar"""
        def loading_animation():
            dots = ""
            progress = 0
            max_width = 400
            
            for i in range(100):  # 5 seconds total (50ms * 100)
                dots = "." * ((i % 4))
                self.loading_label.config(text=f"Loading{dots}")
                
                # Update progress bar
                progress = int((i / 99) * max_width)
                self.progress_bar.config(width=progress)
                
                time.sleep(0.05)
                
            # Close splash screen after loading
            self.root.after(0, self.close_splash)
            
        threading.Thread(target=loading_animation, daemon=True).start()
        
    def close_splash(self):
        """Close the splash screen"""
        self.root.destroy()
        
    def show(self):
        """Show the splash screen"""
        self.root.mainloop()

def show_splash_screen():
    """Function to show splash screen"""
    splash = SplashScreen()
    splash.show()

if __name__ == "__main__":
    show_splash_screen()
