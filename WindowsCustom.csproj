<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>splash.png</ApplicationIcon>
    <AssemblyTitle>WindowsCustom</AssemblyTitle>
    <AssemblyDescription>Advanced Windows Customization System</AssemblyDescription>
    <AssemblyCompany>Aladdin <PERSON>ewa</AssemblyCompany>
    <AssemblyProduct>WindowsCustom</AssemblyProduct>
    <Copyright>Copyright © Aladdin Shenewa 2024</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>

</Project>
