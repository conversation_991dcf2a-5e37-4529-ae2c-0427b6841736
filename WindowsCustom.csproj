<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>splash.png</ApplicationIcon>
    <AssemblyTitle>WindowsCustom</AssemblyTitle>
    <AssemblyDescription>Advanced Windows Customization System</AssemblyDescription>
    <AssemblyCompany>Aladdin <PERSON>ewa</AssemblyCompany>
    <AssemblyProduct>WindowsCustom</AssemblyProduct>
    <Copyright>Copyright © Aladdin Shenewa 2024</Copyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Windows.SDK.Contracts" Version="10.0.22621.2428" />
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Toolkit.Win32.UI.Controls" Version="6.1.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>

</Project>
