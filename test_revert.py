#!/usr/bin/env python3
"""
Test the revert functionality
"""

import subprocess

def test_revert_options():
    """Test the different revert options"""
    print("WindowsCustom Revert Test")
    print("BY Aladdin <PERSON>ewa")
    print("=" * 30)
    
    print("🔄 REVERT OPTIONS AVAILABLE:")
    print()
    
    print("1. 🎨 Quick Revert (from theme button)")
    print("   • Click 🎨 button in taskbar")
    print("   • Select '🔄 Revert All'")
    print("   • Instant revert with confirmation")
    print()
    
    print("2. 🔧 Standalone Revert Tool")
    print("   • Run: python revert_windowscustom.py")
    print("   • Complete system scan and revert")
    print("   • Detailed progress reporting")
    print()
    
    print("3. 📋 GUI Revert Tool")
    print("   • Run: python revert_windowscustom.py --gui")
    print("   • User-friendly interface")
    print("   • Visual confirmation")
    print()
    
    print("4. 🎯 Main Modifier Revert")
    print("   • Run: python permanent_taskbar_modifier.py")
    print("   • Select option 2 (Revert)")
    print("   • Integrated revert option")
    print()
    
    print("5. 🚀 Command Line Revert")
    print("   • Run: python permanent_taskbar_modifier.py --revert")
    print("   • Direct command line revert")
    print("   • No prompts, immediate action")
    print()
    
    print("🛡️ WHAT GETS REVERTED:")
    print("✅ Search box restored")
    print("✅ Task view button restored")
    print("✅ Normal taskbar icons restored")
    print("✅ Widgets/Chat buttons restored (Windows 11)")
    print("✅ Light theme restored")
    print("✅ WindowsCustom startup entry removed")
    print("✅ WindowsCustom registry entries cleaned")
    print("✅ Windows Explorer restarted")
    print()
    
    print("💡 RECOMMENDATION:")
    print("Use option 2 (Standalone Revert Tool) for most reliable results")

def demo_revert_tools():
    """Demo the revert tools"""
    print("\n" + "=" * 50)
    print("REVERT TOOLS DEMO")
    print("=" * 50)
    
    print("\n🎯 CHOOSE A REVERT METHOD TO TEST:")
    print("1. Test standalone revert tool")
    print("2. Test GUI revert tool")
    print("3. Test main modifier revert option")
    print("4. Exit")
    
    choice = input("\nSelect option (1-4): ").strip()
    
    if choice == "1":
        print("\n🔧 Testing standalone revert tool...")
        print("This will check for WindowsCustom modifications")
        try:
            subprocess.run(["python", "revert_windowscustom.py"])
        except Exception as e:
            print(f"Error: {e}")
    
    elif choice == "2":
        print("\n🖼️ Testing GUI revert tool...")
        print("This will open a graphical interface")
        try:
            subprocess.run(["python", "revert_windowscustom.py", "--gui"])
        except Exception as e:
            print(f"Error: {e}")
    
    elif choice == "3":
        print("\n🎯 Testing main modifier revert...")
        print("This will show the main modifier menu")
        try:
            subprocess.run(["python", "permanent_taskbar_modifier.py"])
        except Exception as e:
            print(f"Error: {e}")
    
    elif choice == "4":
        print("Exiting demo")
    
    else:
        print("Invalid choice")

def main():
    """Main function"""
    test_revert_options()
    demo_revert_tools()

if __name__ == "__main__":
    main()
