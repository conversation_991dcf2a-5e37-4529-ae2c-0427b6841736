<Window x:Class="WindowsCustom.SystemStatsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="System Statistics" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        Background="#2D2D2D">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title -->
        <TextBlock Grid.Row="0" Text="System Statistics" 
                   FontSize="24" FontWeight="Bold" 
                   Foreground="White" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- Stats Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Name="StatsPanel">
                <!-- Stats will be added programmatically -->
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Refresh" Width="80" Height="30" 
                   Background="#4CAF50" Foreground="White" 
                   BorderBrush="#45a049" Margin="0,0,10,0"
                   Click="Refresh_Click"/>
            <Button Content="Close" Width="80" Height="30" 
                   Background="#666666" Foreground="White" 
                   BorderBrush="#888888"
                   Click="Close_Click"/>
        </StackPanel>
    </Grid>
</Window>
