#!/usr/bin/env python3
"""
Music Control Interface for WindowsCustom
"""

import threading
import time
import os
from pygame import mixer

class MusicController:
    def __init__(self):
        self.is_playing = False
        self.is_paused = False
        self.current_track = None
        self.music_thread = None
        self.should_stop = False
        
        try:
            mixer.init()
            print("[Music] Controller initialized")
        except Exception as e:
            print(f"[Music] Failed to initialize: {e}")
    
    def play(self):
        """Start/resume music playback"""
        try:
            if self.is_paused:
                mixer.music.unpause()
                self.is_paused = False
                print("[Music] Resumed")
            elif not self.is_playing:
                self.start_music_thread()
            return True
        except Exception as e:
            print(f"[Music] Play error: {e}")
            return False
    
    def pause(self):
        """Pause music playback"""
        try:
            if self.is_playing and not self.is_paused:
                mixer.music.pause()
                self.is_paused = True
                print("[Music] Paused")
                return True
        except Exception as e:
            print(f"[Music] Pause error: {e}")
        return False
    
    def stop(self):
        """Stop music playback"""
        try:
            mixer.music.stop()
            self.is_playing = False
            self.is_paused = False
            self.should_stop = True
            print("[Music] Stopped")
            return True
        except Exception as e:
            print(f"[Music] Stop error: {e}")
        return False
    
    def next_track(self):
        """Skip to next track"""
        try:
            mixer.music.stop()
            # The music thread will automatically move to next track
            print("[Music] Next track")
            return True
        except Exception as e:
            print(f"[Music] Next track error: {e}")
        return False
    
    def get_status(self):
        """Get current music status"""
        return {
            "playing": self.is_playing,
            "paused": self.is_paused,
            "track": self.current_track
        }
    
    def start_music_thread(self):
        """Start the music playback thread"""
        if self.music_thread and self.music_thread.is_alive():
            return
        
        self.should_stop = False
        self.music_thread = threading.Thread(target=self._music_loop, daemon=True)
        self.music_thread.start()
        self.is_playing = True
    
    def _music_loop(self):
        """Internal music loop"""
        music_folder = "music"
        
        if not os.path.exists(music_folder):
            print(f"[Music] Music folder not found: {music_folder}")
            return
        
        # Get music files
        music_files = []
        for file in os.listdir(music_folder):
            if file.lower().endswith(('.mp3', '.wav', '.ogg')):
                music_files.append(file)
        
        music_files.sort()  # Alphabetical order
        
        if not music_files:
            print("[Music] No music files found")
            return
        
        current_index = 0
        
        while not self.should_stop:
            try:
                if current_index >= len(music_files):
                    current_index = 0  # Loop back to start
                
                track_path = os.path.join(music_folder, music_files[current_index])
                self.current_track = music_files[current_index]
                
                print(f"[Music] Playing: {self.current_track}")
                
                mixer.music.load(track_path)
                mixer.music.play()
                
                # Wait for track to finish
                while mixer.music.get_busy() and not self.should_stop:
                    time.sleep(0.1)
                
                current_index += 1
                
            except Exception as e:
                print(f"[Music] Playback error: {e}")
                current_index += 1
                time.sleep(1)
        
        self.is_playing = False
        self.is_paused = False

# Global music controller
music_controller = None

def init_music_controller():
    """Initialize the music controller"""
    global music_controller
    music_controller = MusicController()
    return music_controller

def get_music_controller():
    """Get the music controller instance"""
    global music_controller
    if not music_controller:
        music_controller = init_music_controller()
    return music_controller

def play_music():
    """Play music"""
    controller = get_music_controller()
    return controller.play()

def pause_music():
    """Pause music"""
    controller = get_music_controller()
    return controller.pause()

def stop_music():
    """Stop music"""
    controller = get_music_controller()
    return controller.stop()

def next_track():
    """Next track"""
    controller = get_music_controller()
    return controller.next_track()

def get_music_status():
    """Get music status"""
    controller = get_music_controller()
    return controller.get_status()

if __name__ == "__main__":
    # Test the music controller
    controller = init_music_controller()
    
    print("Music Controller Test")
    print("Commands: play, pause, stop, next, status, quit")
    
    while True:
        cmd = input("> ").strip().lower()
        
        if cmd == "play":
            controller.play()
        elif cmd == "pause":
            controller.pause()
        elif cmd == "stop":
            controller.stop()
        elif cmd == "next":
            controller.next_track()
        elif cmd == "status":
            status = controller.get_status()
            print(f"Status: {status}")
        elif cmd == "quit":
            controller.stop()
            break
        else:
            print("Unknown command")
