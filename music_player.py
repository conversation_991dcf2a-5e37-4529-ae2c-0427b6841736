import os
import time
import threading
import subprocess
import psutil
from pygame import mixer

def is_headphones_connected():
    """Check if headphones are connected using Windows audio devices"""
    try:
        # Use PowerShell to check audio devices
        cmd = 'powershell "Get-AudioDevice -List | Where-Object {$_.Type -eq \'Playback\' -and $_.Default -eq $true} | Select-Object -ExpandProperty Name"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            device_name = result.stdout.strip().lower()
            # Check for common headphone/headset indicators
            headphone_keywords = ['headphone', 'headset', 'earphone', 'bluetooth', 'wireless']
            return any(keyword in device_name for keyword in headphone_keywords)
    except Exception as e:
        print(f"Error checking headphones: {e}")

    # Fallback: assume headphones are connected
    return True

def is_media_playing():
    """Check if other media is playing (YouTube, Netflix, etc.)"""
    try:
        # Check for common media applications
        media_processes = [
            'chrome.exe', 'firefox.exe', 'msedge.exe',  # Browsers (YouTube, etc.)
            'vlc.exe', 'wmplayer.exe', 'spotify.exe',   # Media players
            'netflix.exe', 'disney.exe', 'hulu.exe'     # Streaming apps
        ]

        # Get list of running processes
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                process_name = proc.info['name'].lower()
                if any(media_proc in process_name for media_proc in media_processes):
                    # Check if the process is actually playing audio
                    # This is a simplified check - in reality you'd need to check audio streams
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        return False
    except Exception as e:
        print(f"Error checking media playback: {e}")
        return False

def get_music_files(music_folder):
    """Get sorted list of music files from folder"""
    if not os.path.exists(music_folder):
        print(f"Music folder not found: {music_folder}")
        return []

    music_extensions = ['.mp3', '.wav', '.ogg', '.m4a']
    music_files = []

    for file in os.listdir(music_folder):
        if any(file.lower().endswith(ext) for ext in music_extensions):
            music_files.append(file)

    # Sort alphabetically (top to bottom as requested)
    music_files.sort()
    return music_files

def music_loop(settings):
    """Main music player loop with headphone detection and media pause"""
    try:
        mixer.init()
        print("[Music] Music player initialized")
    except Exception as e:
        print(f"[Music] Failed to initialize mixer: {e}")
        return

    music_folder = settings.get("music_folder", "music")
    require_headphones = settings.get("headphones_only_music", True)
    current_track_index = 0
    is_paused = False

    print(f"[Music] Starting music loop, folder: {music_folder}")
    print(f"[Music] Headphones required: {require_headphones}")

    while True:
        try:
            # Check headphone requirement
            if require_headphones and not is_headphones_connected():
                if not is_paused:
                    print("[Music] No headphones detected, pausing music")
                    if mixer.music.get_busy():
                        mixer.music.pause()
                    is_paused = True
                time.sleep(5)
                continue

            # Resume if was paused due to headphones
            if is_paused and require_headphones and is_headphones_connected():
                print("[Music] Headphones detected, resuming music")
                if mixer.music.get_busy():
                    mixer.music.unpause()
                is_paused = False

            # Get music files
            music_files = get_music_files(music_folder)
            if not music_files:
                print("[Music] No music files found, waiting...")
                time.sleep(30)
                continue

            # Handle track selection
            if current_track_index >= len(music_files):
                current_track_index = 0  # Loop back to beginning

            current_track = music_files[current_track_index]
            track_path = os.path.join(music_folder, current_track)

            # Check if other media is playing
            if is_media_playing():
                if not is_paused:
                    print("[Music] Other media detected, pausing music")
                    if mixer.music.get_busy():
                        mixer.music.pause()
                    is_paused = True
                time.sleep(2)
                continue

            # Resume if was paused due to other media
            if is_paused and not is_media_playing():
                print("[Music] Other media stopped, resuming music")
                if mixer.music.get_busy():
                    mixer.music.unpause()
                    is_paused = False
                    time.sleep(1)
                    continue

            # Start playing new track if not currently playing
            if not mixer.music.get_busy() and not is_paused:
                try:
                    print(f"[Music] Playing: {current_track}")
                    mixer.music.load(track_path)
                    mixer.music.play()
                    current_track_index += 1
                except Exception as e:
                    print(f"[Music] Failed to play {current_track}: {e}")
                    current_track_index += 1
                    continue

            time.sleep(1)  # Check every second

        except Exception as e:
            print(f"[Music] Error in music loop: {e}")
            time.sleep(5)
