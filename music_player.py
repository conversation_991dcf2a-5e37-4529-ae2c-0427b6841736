import os
import time
import threading
from pygame import mixer

def is_headphones_connected():
    # Placeholder: Windows does not provide a simple cross-module way in Python
    # You can extend this with Windows Core Audio APIs via pycaw or others
    return True

def is_media_playing():
    # Placeholder: Could hook into system audio or detect active windows
    # For simplicity, returns False (no media playing)
    return False

def music_loop(settings):
    mixer.init()
    music_folder = settings.get("music_folder", "music")
    require_headphones = settings.get("music_requires_headphones", True)

    while True:
        if require_headphones and not is_headphones_connected():
            time.sleep(10)
            continue

        files = [f for f in os.listdir(music_folder) if f.lower().endswith(".mp3")]
        files.sort()

        for track in files:
            if is_media_playing():
                mixer.music.pause()
                while is_media_playing():
                    time.sleep(1)
                mixer.music.unpause()
            try:
                mixer.music.load(os.path.join(music_folder, track))
                mixer.music.play()
                while mixer.music.get_busy():
                    time.sleep(1)
            except Exception as e:
                print(f"[Music] Failed to play {track}: {e}")
