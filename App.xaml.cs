using System;
using System.Windows;

namespace WindowsCustom
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Ensure only one instance is running
            var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
            var processes = System.Diagnostics.Process.GetProcessesByName(currentProcess.ProcessName);
            
            if (processes.Length > 1)
            {
                MessageBox.Show("WindowsCustom is already running!", "WindowsCustom", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
                Current.Shutdown();
                return;
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // Ensure Windows taskbar is restored on exit
            try
            {
                var taskbarManager = new TaskbarManager();
                taskbarManager.ShowWindowsTaskbar();
            }
            catch
            {
                // Ignore errors during shutdown
            }
            
            base.OnExit(e);
        }
    }
}
