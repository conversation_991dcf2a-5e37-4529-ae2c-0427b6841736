# WindowsCustom Theme Colors
**BY <PERSON>addin <PERSON>ewa**

## 🎨 Exact Color Specifications

### Red Theme
- **Background**: `#FF6B6B` (Light Red)
- **Buttons**: `#CC5555` (Dark Red)  
- **Text**: `#FFFFFF` (White)

### Hatsune Miku Theme ✨
- **Background**: `#66BFF2` (Miku Blue)
- **Buttons**: `#39C5BB` (Hatsune Miku Shirt Color - Teal/Turquoise)
- **Text**: `#1E3A8A` (Dark Blue)

### Windows Dark Theme
- **Background**: `#1F1F1F` (Dark Gray)
- **Buttons**: `#2D2D2D` (Medium Gray)
- **Text**: `#FFFFFF` (White)

## 🖼️ Visual Preview

### Hatsune Miku Theme Layout:
```
┌─────────────────────────────────────────────────────────────┐
│ ⊞ 🌤️  │ 🌐Chrome 💻VSCode 📁Explorer │ ⚙️📰🎥 │ 🔋85% 12:34 │
│ MIKU  │                              │        │ WED/JUN    │
└─────────────────────────────────────────────────────────────┘
```
- Background: Miku blue (`#66BFF2`)
- Buttons: Shirt color (`#39C5BB`) 
- Text: Dark blue (`#1E3A8A`)

### Red Theme Layout:
```
┌─────────────────────────────────────────────────────────────┐
│ ⊞ 🌤️  │ 🌐Chrome 💻VSCode 📁Explorer │ ⚙️📰🎥 │ 🔋85% 12:34 │
│ RED   │                              │        │ WED/JUN    │
└─────────────────────────────────────────────────────────────┘
```
- Background: Light red (`#FF6B6B`)
- Buttons: Dark red (`#CC5555`)
- Text: White (`#FFFFFF`)

## 🔧 How to Change Themes

1. **Open Settings GUI:**
   ```bash
   python settings_gui.py
   ```

2. **Select Theme:**
   - Click "Red Theme" or "Hatsune Miku Theme" button
   - See live preview of colors
   - Click "Save Settings"

3. **Apply Theme:**
   ```bash
   python demo_taskbar.py
   ```

## 📁 Theme Configuration

The themes are stored in `settings.json`:

```json
{
  "current_theme": "Hatsune Miku",
  "themes": {
    "Red": {
      "background": "#FF6B6B",
      "buttons": "#CC5555",
      "text": "#FFFFFF"
    },
    "Hatsune Miku": {
      "background": "#66BFF2",
      "buttons": "#39C5BB",
      "text": "#1E3A8A"
    },
    "Windows Dark": {
      "background": "#1F1F1F",
      "buttons": "#2D2D2D",
      "text": "#FFFFFF"
    }
  }
}
```

## 🎯 Color Reference Tools

- **View All Colors**: `python color_reference.py`
- **Test Themes**: `python test_themes.py`
- **Complete Demo**: `python demo_complete_system.py`

---

**Note**: The Hatsune Miku theme uses authentic colors:
- **Shirt color** for buttons (`#39C5BB`)
- **Dark blue** for text (`#1E3A8A`)
- **Miku blue** for background (`#66BFF2`)
