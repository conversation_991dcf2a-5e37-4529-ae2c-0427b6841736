#!/usr/bin/env python3
"""
Test the permanent taskbar modifier
"""

import os
import subprocess
import winreg

def test_admin_rights():
    """Test if running with admin rights"""
    print("🔐 Testing administrator rights...")
    
    try:
        # Try to access a protected registry key
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SOFTWARE\Microsoft\Windows\CurrentVersion", 
                           0, winreg.KEY_READ):
            pass
        print("✅ Administrator rights available")
        return True
    except PermissionError:
        print("❌ Administrator rights required")
        print("💡 Right-click and 'Run as administrator'")
        return False

def test_registry_access():
    """Test registry modification access"""
    print("\n🔧 Testing registry access...")
    
    try:
        # Test writing to user registry
        test_key = r"SOFTWARE\WindowsCustomTest"
        with winreg.CreateKey(winreg.HKEY_CURRENT_USER, test_key) as key:
            winreg.SetValueEx(key, "TestValue", 0, winreg.REG_SZ, "test")
        
        # Clean up test key
        winreg.DeleteKey(winreg.HKEY_CURRENT_USER, test_key)
        
        print("✅ Registry write access confirmed")
        return True
    except Exception as e:
        print(f"❌ Registry access error: {e}")
        return False

def test_taskbar_detection():
    """Test Windows taskbar detection"""
    print("\n🔍 Testing taskbar detection...")
    
    try:
        import win32gui
        
        # Find taskbar
        taskbar = win32gui.FindWindow("Shell_TrayWnd", None)
        if taskbar:
            rect = win32gui.GetWindowRect(taskbar)
            print(f"✅ Taskbar found: {taskbar}")
            print(f"📍 Position: {rect}")
            
            # Find start button
            start_btn = win32gui.FindWindowEx(taskbar, 0, "Start", None)
            if not start_btn:
                start_btn = win32gui.FindWindowEx(taskbar, 0, "Button", None)
            
            if start_btn:
                print(f"✅ Start button found: {start_btn}")
            else:
                print("⚠️  Start button not found (may still work)")
            
            return True
        else:
            print("❌ Taskbar not found")
            return False
            
    except Exception as e:
        print(f"❌ Taskbar detection error: {e}")
        return False

def show_modification_plan():
    """Show what modifications will be made"""
    print("\n" + "=" * 50)
    print("PERMANENT TASKBAR MODIFICATIONS")
    print("=" * 50)
    
    print("\n🎯 WHAT WILL BE CHANGED:")
    print("✅ Hide Windows search box")
    print("✅ Enable small taskbar icons")
    print("✅ Hide task view button")
    print("✅ Hide widgets button")
    print("✅ Hide chat button")
    print("✅ Apply custom theme colors")
    print("✅ Add persistent music controls")
    print("✅ Add persistent settings controls")
    print("✅ Create startup entry for persistence")
    
    print("\n🔄 PROCESS:")
    print("1. Modify Windows registry settings")
    print("2. Apply custom theme to Windows")
    print("3. Restart Windows Explorer")
    print("4. Add custom control overlays")
    print("5. Create startup entry")
    print("6. Keep controls running")
    
    print("\n💾 PERSISTENCE:")
    print("✅ Changes survive Windows restart")
    print("✅ Controls auto-start on boot")
    print("✅ Theme settings preserved")
    print("✅ Registry modifications permanent")
    
    print("\n⚠️  IMPORTANT:")
    print("• This modifies Windows registry")
    print("• Windows Explorer will restart")
    print("• Changes are permanent")
    print("• Backup recommended")

def run_permanent_modifier():
    """Run the permanent taskbar modifier"""
    print("\n🚀 STARTING PERMANENT MODIFIER")
    print("=" * 40)
    
    print("⚠️  WARNING: This will permanently modify your taskbar!")
    print("Windows Explorer will restart during the process.")
    
    choice = input("\nContinue with permanent modifications? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n🔄 Starting permanent taskbar modifier...")
        
        try:
            # Run the permanent modifier
            subprocess.run(["python", "permanent_taskbar_modifier.py"])
            
        except KeyboardInterrupt:
            print("\n🛑 Modifier stopped by user")
        except Exception as e:
            print(f"❌ Modifier error: {e}")
    else:
        print("Operation cancelled")

def main():
    """Main test function"""
    print("WindowsCustom Permanent Taskbar Modifier Test")
    print("BY Aladdin Shenewa")
    print("=" * 50)
    
    # Run tests
    tests_passed = 0
    
    if test_admin_rights():
        tests_passed += 1
    
    if test_registry_access():
        tests_passed += 1
    
    if test_taskbar_detection():
        tests_passed += 1
    
    print(f"\n📊 TESTS PASSED: {tests_passed}/3")
    
    if tests_passed == 3:
        print("✅ All tests passed! System ready for permanent modification.")
        show_modification_plan()
        run_permanent_modifier()
    else:
        print("❌ Some tests failed. Cannot proceed with modifications.")
        
        if tests_passed == 0:
            print("\n💡 SOLUTIONS:")
            print("• Run as administrator")
            print("• Check Windows version compatibility")
            print("• Ensure pywin32 is installed")

if __name__ == "__main__":
    main()
