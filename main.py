import os
import shutil
import threading
import time
import webbrowser
import tkinter as tk
from tkinter import messagebox, PhotoImage, ttk
from datetime import datetime, timedelta
import subprocess
import glob

import backup
import music_player
import taskbar
import utils
import splash_screen
import edge_blocker

# Globals for timer
BREAK_LENGTH_MIN = 20
WORK_LIMIT_MIN = 59

CONFIG_FILE = "settings.json"

def load_settings():
    import json
    if not os.path.exists(CONFIG_FILE):
        default = {
            "run_on_start": True,
            "enable_music": True,
            "music_folder": "music",
            "enable_backup": True,
            "backup_folders": [
                "Desktop", "Documents", "Downloads"
            ],
            "onedrive_backup_folder": os.path.expandvars(r"%UserProfile%\\OneDrive\\WindowsCustomBackups"),
            "taskbar_color": "#57C7E3",  # Hatsune Miku blue
            "security_check": True,
            "pause_music_on_media": True,
            "min_text_size": 15,
            "auto_fix_vscode": True,
            "keyboard_light_on_vscode": True,
            "daily_trash_cleanup": True,
            "cookie_cleanup_after_days": 30,
            "fake_bsod_enabled": True,
            "fake_bsod_image": "assets/bsod.png",
            "battery_warning_threshold": 10,
            "news_url": "https://www.youtube.com/@cybernews",
            "music_requires_headphones": True
        }
        with open(CONFIG_FILE, "w") as f:
            json.dump(default, f, indent=2)
        return default
    else:
        with open(CONFIG_FILE, "r") as f:
            return json.load(f)

settings = load_settings()

class BreakScreen(tk.Toplevel):
    def __init__(self, root, minutes):
        super().__init__(root)
        self.minutes = minutes
        self.title("Break Time!")
        self.geometry(f"{root.winfo_screenwidth()}x{root.winfo_screenheight()}+0+0")
        self.configure(bg="grey")
        self.attributes("-topmost", True)
        self.protocol("WM_DELETE_WINDOW", self.prevent_close)

        self.label_countdown = tk.Label(self, text="", font=("Arial", 36), fg="red", bg="grey")
        self.label_countdown.pack(pady=50)
        self.label_message = tk.Label(self, text="Curtsy of your eyes, wait 20 minutes until you can get back on!",
                                      font=("Arial", 24), bg="grey", fg="white")
        self.label_message.pack(pady=20)
        self.progress = ttk.Progressbar(self, orient="horizontal", length=600, mode="determinate", maximum=minutes*60)
        self.progress.pack(pady=20)

        self.start_time = time.time()
        self.update_timer()

    def update_timer(self):
        elapsed = time.time() - self.start_time
        remaining = self.minutes*60 - elapsed
        if remaining <= 0:
            self.destroy()
            return
        self.progress['value'] = elapsed
        mins = int(remaining // 60)
        secs = int(remaining % 60)
        self.label_countdown.config(text=f"{mins:02d}:{secs:02d} remaining")
        self.after(1000, self.update_timer)

    def prevent_close(self):
        pass  # Do not allow user to close break screen

def daily_trash_cleanup():
    # Delete trash images of google, keep custom media until get or system deletes.
    # This is a stub implementation; user should customize paths & rules
    recycle_bin = os.path.expandvars(r"%USERPROFILE%\Recycle.Bin")
    # Complex to delete images from recycle bin programmatically on Windows without admin
    # So skip actual deletion here. Placeholder:
    print("[Cleanup] Daily trash cleanup placeholder - customize for your setup.")

def check_battery_and_show_bsod(root):
    try:
        import psutil
        battery = psutil.sensors_battery()
        if battery is None:
            return
        if battery.percent <= settings.get("battery_warning_threshold", 10) and not battery.power_plugged:
            bsod_win = tk.Toplevel(root)
            bsod_win.attributes("-topmost", True)
            bsod_win.geometry(f"{root.winfo_screenwidth()}x{root.winfo_screenheight()}+0+0")
            bsod_win.overrideredirect(True)
            img = tk.PhotoImage(file=settings.get("fake_bsod_image"))
            label = tk.Label(bsod_win, image=img)
            label.image = img
            label.pack()
            # Hide after charging connected:
            def poll_battery():
                while True:
                    b = psutil.sensors_battery()
                    if b.power_plugged:
                        bsod_win.destroy()
                        break
                    time.sleep(5)
            threading.Thread(target=poll_battery, daemon=True).start()
    except ImportError:
        print("[Warning] psutil not installed; battery check disabled.")

def clear_temp_folder():
    temp_path = os.path.expandvars(r"%TEMP%")
    for root, dirs, files in os.walk(temp_path):
        for name in files:
            file_path = os.path.join(root, name)
            try:
                os.remove(file_path)
                print(f"[Temp] Deleted file: {file_path}")
            except PermissionError:
                print(f"[Temp] Skipped locked file: {file_path}")
        for name in dirs:
            dir_path = os.path.join(root, name)
            try:
                shutil.rmtree(dir_path)
                print(f"[Temp] Deleted folder: {dir_path}")
            except PermissionError:
                print(f"[Temp] Skipped locked folder: {dir_path}")

def main():
    # Show splash screen first
    print("Starting WindowsCustom...")
    splash_thread = threading.Thread(target=splash_screen.show_splash_screen, daemon=True)
    splash_thread.start()

    # Wait for splash to show
    time.sleep(1)

    root = tk.Tk()
    root.withdraw()  # Hide root window for now

    # Setup Edge blocking
    print("Setting up Edge blocking...")
    edge_blocker.setup_edge_blocking()

    # Clear temp folder
    print("Cleaning temp folder...")
    clear_temp_folder()

    # Run daily trash cleanup in background
    if settings.get("daily_trash_cleanup", True):
        threading.Thread(target=daily_trash_cleanup, daemon=True).start()

    # Show fake BSOD if battery low
    if settings.get("fake_bsod_enabled", True):
        check_battery_and_show_bsod(root)

    # Start backup thread if enabled
    if settings.get("enable_backup", True):
        print("Starting backup system...")
        threading.Thread(target=backup.start_backup, daemon=True).start()

    # Start music player thread if enabled
    if settings.get("enable_music", True):
        print("Starting music player...")
        music_thread = threading.Thread(target=music_player.music_loop, args=(settings,), daemon=True)
        music_thread.start()

    # Wait for splash to finish (about 5 seconds)
    time.sleep(6)

    # Initialize and show the taskbar
    print("Initializing custom taskbar...")
    taskbar_thread = threading.Thread(target=taskbar.init_taskbar, args=(settings,), daemon=True)
    taskbar_thread.start()

    # Timer for break enforcement (simplified)
    def work_timer_loop():
        start_time = datetime.now()
        while True:
            elapsed = datetime.now() - start_time
            if elapsed > timedelta(minutes=WORK_LIMIT_MIN):
                # Show break screen (blocks)
                root.after(0, lambda: BreakScreen(root, BREAK_LENGTH_MIN))
                # Pause timer until break screen is closed
                while any(isinstance(w, BreakScreen) for w in root.winfo_children()):
                    time.sleep(1)
                start_time = datetime.now()  # reset after break
            time.sleep(10)

    threading.Thread(target=work_timer_loop, daemon=True).start()

    print("WindowsCustom system fully loaded!")
    root.mainloop()

if __name__ == "__main__":
    main()
