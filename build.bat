@echo off
echo WindowsCustom Build Script
echo BY Aladdin Shenewa
echo ========================

echo.
echo Checking for .NET 6.0...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET 6.0 is not installed!
    echo Please install .NET 6.0 Desktop Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo .NET 6.0 found!
echo.

echo Restoring NuGet packages...
dotnet restore
if errorlevel 1 (
    echo ERROR: Failed to restore packages!
    pause
    exit /b 1
)

echo.
echo Building WindowsCustom...
dotnet build --configuration Release
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================
echo BUILD SUCCESSFUL!
echo ========================
echo.
echo Executable location:
echo bin\Release\net6.0-windows\WindowsCustom.exe
echo.
echo To run WindowsCustom:
echo 1. cd bin\Release\net6.0-windows
echo 2. WindowsCustom.exe
echo.
pause
