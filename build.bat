@echo off
echo WindowsCustom Build Script
echo BY Aladdin Shenewa
echo ========================

echo.
echo Checking for .NET...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: .NET is not installed!
    echo Please install .NET Desktop Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet
    pause
    exit /b 1
)

echo .NET found!
echo.

echo Restoring NuGet packages...
dotnet restore WindowsCustom.csproj
if errorlevel 1 (
    echo ERROR: Failed to restore packages!
    pause
    exit /b 1
)

echo.
echo Building WindowsCustom...
dotnet build WindowsCustom.csproj --configuration Release
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================
echo BUILD SUCCESSFUL!
echo ========================
echo.
echo Executable location:
echo bin\Release\net9.0-windows\WindowsCustom.exe
echo.
echo To run WindowsCustom:
echo 1. cd bin\Release\net9.0-windows
echo 2. WindowsCustom.exe
echo.
pause
