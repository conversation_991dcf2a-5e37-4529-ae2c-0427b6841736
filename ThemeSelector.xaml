<Window x:Class="WindowsCustom.ThemeSelector"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Theme Selector" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#2D2D2D">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title -->
        <TextBlock Grid.Row="0" Text="Select Theme" 
                   FontSize="24" FontWeight="Bold" 
                   Foreground="White" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- Theme Grid -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <UniformGrid Name="ThemeGrid" Columns="2" Margin="0,0,0,20">
                <!-- Themes will be added programmatically -->
            </UniformGrid>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button Content="Cancel" Width="80" Height="30" 
                   Background="#666666" Foreground="White" 
                   BorderBrush="#888888" Margin="0,0,10,0"
                   Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
