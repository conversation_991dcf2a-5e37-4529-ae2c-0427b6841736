import os
import subprocess
import winreg
import webbrowser
import threading
import time
from urllib.parse import urlparse

class EdgeBlocker:
    def __init__(self):
        self.chrome_path = self.find_chrome_path()
        self.blocked_processes = ['msedge.exe', 'MicrosoftEdge.exe']
        self.monitoring = False
        
    def find_chrome_path(self):
        """Find Chrome installation path"""
        possible_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def set_chrome_as_default_browser(self):
        """Set Chrome as default browser (requires admin rights)"""
        if not self.chrome_path:
            print("Chrome not found, cannot set as default")
            return False
            
        try:
            # This is a simplified approach - full implementation would need more registry changes
            key_path = r"SOFTWARE\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "ProgId", 0, winreg.REG_SZ, "ChromeHTML")
            
            key_path = r"SOFTWARE\Microsoft\Windows\Shell\Associations\UrlAssociations\https\UserChoice"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "ProgId", 0, winreg.REG_SZ, "ChromeHTML")
                
            print("Chrome set as default browser")
            return True
        except Exception as e:
            print(f"Could not set Chrome as default: {e}")
            return False
    
    def redirect_edge_links(self, url):
        """Redirect Edge links to Chrome"""
        if self.chrome_path and url:
            try:
                subprocess.Popen([self.chrome_path, url])
                print(f"Redirected to Chrome: {url}")
                return True
            except Exception as e:
                print(f"Failed to redirect to Chrome: {e}")
        return False
    
    def kill_edge_processes(self):
        """Kill any running Edge processes"""
        for process_name in self.blocked_processes:
            try:
                subprocess.run(['taskkill', '/f', '/im', process_name], 
                             capture_output=True, check=False)
            except Exception as e:
                print(f"Error killing {process_name}: {e}")
    
    def monitor_edge_processes(self):
        """Monitor and kill Edge processes continuously"""
        self.monitoring = True
        while self.monitoring:
            try:
                # Check for running Edge processes
                result = subprocess.run(['tasklist', '/fi', 'imagename eq msedge.exe'], 
                                      capture_output=True, text=True)
                
                if 'msedge.exe' in result.stdout:
                    print("Edge detected, terminating...")
                    self.kill_edge_processes()
                    
                time.sleep(2)  # Check every 2 seconds
            except Exception as e:
                print(f"Error monitoring Edge: {e}")
                time.sleep(5)
    
    def start_monitoring(self):
        """Start monitoring Edge processes in background"""
        if not self.monitoring:
            monitor_thread = threading.Thread(target=self.monitor_edge_processes, daemon=True)
            monitor_thread.start()
            print("Edge monitoring started")
    
    def stop_monitoring(self):
        """Stop monitoring Edge processes"""
        self.monitoring = False
        print("Edge monitoring stopped")
    
    def block_edge_startup(self):
        """Prevent Edge from starting up automatically"""
        try:
            # Disable Edge startup entries
            startup_locations = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run"
            ]
            
            for location in startup_locations:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, location, 0, winreg.KEY_SET_VALUE) as key:
                        # Remove Edge startup entries if they exist
                        edge_entries = ['MicrosoftEdgeAutoLaunch', 'EdgeUpdate']
                        for entry in edge_entries:
                            try:
                                winreg.DeleteValue(key, entry)
                                print(f"Removed Edge startup entry: {entry}")
                            except FileNotFoundError:
                                pass  # Entry doesn't exist
                except Exception as e:
                    print(f"Could not access startup registry: {e}")
                    
        except Exception as e:
            print(f"Error blocking Edge startup: {e}")
    
    def setup_edge_blocking(self):
        """Set up complete Edge blocking system"""
        print("Setting up Edge blocking...")
        
        # Kill any running Edge processes
        self.kill_edge_processes()
        
        # Try to set Chrome as default
        self.set_chrome_as_default_browser()
        
        # Block Edge startup
        self.block_edge_startup()
        
        # Start monitoring
        self.start_monitoring()
        
        print("Edge blocking setup complete")

# Global instance
edge_blocker = EdgeBlocker()

def setup_edge_blocking():
    """Setup function to be called from main"""
    edge_blocker.setup_edge_blocking()

def redirect_to_chrome(url):
    """Function to redirect URLs to Chrome"""
    return edge_blocker.redirect_edge_links(url)

if __name__ == "__main__":
    # Test the edge blocker
    setup_edge_blocking()
    
    # Keep running to monitor
    try:
        while True:
            time.sleep(10)
    except KeyboardInterrupt:
        edge_blocker.stop_monitoring()
        print("Edge blocker stopped")
