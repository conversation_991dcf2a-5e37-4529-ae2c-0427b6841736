import os
import shutil
from datetime import datetime

USERNAME = os.getenv("USERNAME")
LOCAL_BACKUP_ROOT = os.path.join(os.getcwd(), "Backups")
ONEDRIVE_ROOT = os.path.join(os.path.expandvars(r"%UserProfile%"), "OneDrive", "WindowsCustomBackups")

COMMON_APP_KEYWORDS = [
    "vscode", "chrome", "obs", "audacity", "discord",
    "vlc", "notepad++", "python", "unity", "blender", "node_modules"
]

def is_unique_file(file_path):
    file_path = file_path.lower()
    for keyword in COMMON_APP_KEYWORDS:
        if keyword in file_path:
            return False
    return True

def should_backup(src, dest):
    if not os.path.exists(dest):
        return True
    return os.path.getmtime(src) > os.path.getmtime(dest)

def copy_file(src, dest):
    try:
        os.makedirs(os.path.dirname(dest), exist_ok=True)
        shutil.copy2(src, dest)
        print(f"[Backup] Copied: {src}")
    except Exception as e:
        print(f"[Backup] Failed to copy {src}: {e}")

def backup_folder(src_folder, dest_folder, cloud_folder):
    for root, _, files in os.walk(src_folder):
        rel_path = os.path.relpath(root, src_folder)
        local_root = os.path.join(dest_folder, rel_path)
        cloud_root = os.path.join(cloud_folder, rel_path)

        for file in files:
            src_file = os.path.join(root, file)
            local_file = os.path.join(local_root, file)
            cloud_file = os.path.join(cloud_root, file)

            if should_backup(src_file, local_file):
                copy_file(src_file, local_file)

                if is_unique_file(src_file):
                    copy_file(src_file, cloud_file)

def start_backup():
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    local_backup_dir = os.path.join(LOCAL_BACKUP_ROOT, f"backup_{timestamp}")
    cloud_backup_dir = os.path.join(ONEDRIVE_ROOT, f"backup_{timestamp}")

    folders = [
        os.path.expandvars(r"%UserProfile%\Desktop"),
        os.path.expandvars(r"%UserProfile%\Documents"),
        os.path.expandvars(r"%UserProfile%\Downloads")
    ]

    for folder in folders:
        if os.path.exists(folder):
            folder_name = os.path.basename(folder)
            local_dest = os.path.join(local_backup_dir, folder_name)
            cloud_dest = os.path.join(cloud_backup_dir, folder_name)
            backup_folder(folder, local_dest, cloud_dest)
        else:
            print(f"[Backup] Skipped missing folder: {folder}")

    print("[Backup] Smart backup complete. OneDrive will sync in background.")
