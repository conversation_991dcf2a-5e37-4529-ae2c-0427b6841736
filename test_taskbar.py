#!/usr/bin/env python3
"""
Test the taskbar functionality independently
"""

import tkinter as tk
import json
import os

def load_settings():
    """Load settings from JSON file"""
    if os.path.exists("settings.json"):
        with open("settings.json", "r") as f:
            return json.load(f)
    return {}

def test_taskbar():
    """Test taskbar creation"""
    print("Testing taskbar...")
    
    # Load settings
    settings = load_settings()
    print(f"Loaded {len(settings)} settings")
    
    # Import and test taskbar
    try:
        import taskbar
        print("Taskbar module imported successfully")
        
        # Test individual functions
        print(f"Time: {taskbar.get_time_string()}")
        print(f"Date: {taskbar.get_date_string()}")
        
        battery_info = taskbar.get_battery_status()
        print(f"Battery: {battery_info}")
        
        most_used = taskbar.get_most_used_apps()
        print(f"Most used apps: {len(most_used)} found")
        for app in most_used:
            print(f"  - {app['name']}: {app.get('count', 0)} uses")
        
        print("All taskbar functions working!")
        
        # Test taskbar creation (will show the taskbar)
        print("Creating taskbar window...")
        print("Press Ctrl+C to close")
        
        try:
            taskbar.init_taskbar(settings)
        except KeyboardInterrupt:
            print("Taskbar closed by user")
        
    except Exception as e:
        print(f"Taskbar test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_taskbar()
