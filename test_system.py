#!/usr/bin/env python3
"""
Test script for WindowsCustom system components
"""

import sys
import traceback

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter")
    except Exception as e:
        print(f"✗ tkinter: {e}")
        return False
    
    try:
        import pygame
        print("✓ pygame")
    except Exception as e:
        print(f"✗ pygame: {e}")
        return False
    
    try:
        import psutil
        print("✓ psutil")
    except Exception as e:
        print(f"✗ psutil: {e}")
        return False
    
    try:
        import requests
        print("✓ requests")
    except Exception as e:
        print(f"✗ requests: {e}")
        return False
    
    return True

def test_modules():
    """Test our custom modules"""
    print("\nTesting custom modules...")
    
    try:
        import splash_screen
        print("✓ splash_screen")
    except Exception as e:
        print(f"✗ splash_screen: {e}")
        traceback.print_exc()
        return False
    
    try:
        import taskbar
        print("✓ taskbar")
    except Exception as e:
        print(f"✗ taskbar: {e}")
        traceback.print_exc()
        return False
    
    try:
        import music_player
        print("✓ music_player")
    except Exception as e:
        print(f"✗ music_player: {e}")
        traceback.print_exc()
        return False
    
    try:
        import edge_blocker
        print("✓ edge_blocker")
    except Exception as e:
        print(f"✗ edge_blocker: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_splash_screen():
    """Test splash screen functionality"""
    print("\nTesting splash screen...")
    
    try:
        import splash_screen
        import threading
        import time
        
        # Test splash screen creation (don't show it)
        splash = splash_screen.SplashScreen()
        print("✓ Splash screen created successfully")
        splash.root.destroy()  # Clean up
        return True
    except Exception as e:
        print(f"✗ Splash screen test failed: {e}")
        traceback.print_exc()
        return False

def test_settings():
    """Test settings loading"""
    print("\nTesting settings...")
    
    try:
        import json
        import os
        
        if os.path.exists("settings.json"):
            with open("settings.json", "r") as f:
                settings = json.load(f)
            print("✓ Settings loaded successfully")
            print(f"  - Found {len(settings)} settings")
            return True
        else:
            print("✗ settings.json not found")
            return False
    except Exception as e:
        print(f"✗ Settings test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("WindowsCustom System Test")
    print("=" * 30)
    
    tests = [
        test_imports,
        test_modules,
        test_splash_screen,
        test_settings
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
            traceback.print_exc()
    
    print(f"\nTest Results: {passed}/{total} passed")
    
    if passed == total:
        print("✓ All tests passed! System should work correctly.")
    else:
        print("✗ Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
