#!/usr/bin/env python3
"""
Test the new theme system for WindowsCustom
"""

import json
import os

def test_theme_system():
    """Test the theme system"""
    print("WindowsCustom Theme System Test")
    print("=" * 40)
    
    # Load settings
    if os.path.exists("settings.json"):
        with open("settings.json", "r") as f:
            settings = json.load(f)
    else:
        print("❌ settings.json not found")
        return False
    
    # Check themes
    themes = settings.get("themes", {})
    current_theme = settings.get("current_theme", "")
    
    print(f"Current Theme: {current_theme}")
    print(f"Available Themes: {list(themes.keys())}")
    print()
    
    # Display each theme
    for theme_name, theme_colors in themes.items():
        print(f"🎨 {theme_name} Theme:")
        print(f"   Background: {theme_colors['background']}")
        print(f"   Buttons:    {theme_colors['buttons']}")
        print(f"   Text:       {theme_colors['text']}")
        
        # Show color preview (text representation)
        if theme_name == "Red":
            print("   Preview:    🔴 Red theme with dark red buttons")
        elif theme_name == "Hatsune Miku":
            print("   Preview:    💙 Miku blue with shirt-colored buttons & dark blue text")
        elif theme_name == "Windows Dark":
            print("   Preview:    ⚫ Dark Windows 11 style")
        print()
    
    # Test taskbar colors
    taskbar_colors = settings.get("taskbar_colors", {})
    print("Current Taskbar Colors:")
    print(f"   Background: {taskbar_colors.get('background', 'Not set')}")
    print(f"   Buttons:    {taskbar_colors.get('buttons', 'Not set')}")
    print(f"   Text:       {taskbar_colors.get('text', 'Not set')}")
    print()
    
    return True

def demo_settings_gui():
    """Demo the settings GUI"""
    print("🚀 Testing Settings GUI...")
    
    try:
        import settings_gui
        print("✅ Settings GUI module loaded successfully")
        
        print("\nTo test the GUI, run:")
        print("python settings_gui.py")
        print("\nOr click the settings button in the taskbar")
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import settings GUI: {e}")
        return False

def demo_taskbar_themes():
    """Demo taskbar with different themes"""
    print("🎨 Taskbar Theme Demo")
    print("=" * 30)
    
    # Load settings
    if os.path.exists("settings.json"):
        with open("settings.json", "r") as f:
            settings = json.load(f)
    else:
        print("❌ settings.json not found")
        return
    
    current_theme = settings.get("current_theme", "Hatsune Miku")
    
    print(f"Current theme: {current_theme}")
    print("\nTaskbar Layout Preview:")
    
    if current_theme == "Red":
        print("┌─────────────────────────────────────────────────────────────┐")
        print("│ ⊞ 🌤️  │ 🌐Chrome 💻VSCode 📁Explorer │ ⚙️📰🎥 │ 🔋85% 12:34 │")
        print("│ RED   │                              │        │ WED/JUN    │")
        print("└─────────────────────────────────────────────────────────────┘")
        print("Colors: Light red background, dark red buttons, white text")
    
    elif current_theme == "Hatsune Miku":
        print("┌─────────────────────────────────────────────────────────────┐")
        print("│ ⊞ 🌤️  │ 🌐Chrome 💻VSCode 📁Explorer │ ⚙️📰🎥 │ 🔋85% 12:34 │")
        print("│ MIKU  │                              │        │ WED/JUN    │")
        print("└─────────────────────────────────────────────────────────────┘")
        print("Colors: Miku blue background, shirt-colored buttons, dark blue text")
    
    else:
        print("┌─────────────────────────────────────────────────────────────┐")
        print("│ ⊞ 🌤️  │ 🌐Chrome 💻VSCode 📁Explorer │ ⚙️📰🎥 │ 🔋85% 12:34 │")
        print("│ DARK  │                              │        │ WED/JUN    │")
        print("└─────────────────────────────────────────────────────────────┘")
        print("Colors: Dark Windows 11 style")
    
    print("\n💡 To change themes:")
    print("1. Run: python settings_gui.py")
    print("2. Go to 'Color Themes' tab")
    print("3. Click your preferred theme button")
    print("4. Click 'Save Settings'")
    print("5. Restart the taskbar to see changes")

def main():
    """Main test function"""
    print("WindowsCustom Theme System")
    print("BY Aladdin Shenewa")
    print("=" * 50)
    print()
    
    # Test theme system
    if test_theme_system():
        print("✅ Theme system working correctly")
    else:
        print("❌ Theme system has issues")
    
    print()
    
    # Test settings GUI
    if demo_settings_gui():
        print("✅ Settings GUI available")
    else:
        print("❌ Settings GUI not available")
    
    print()
    
    # Demo taskbar themes
    demo_taskbar_themes()
    
    print("\n🚀 Ready to use!")
    print("Run: python demo_taskbar.py")

if __name__ == "__main__":
    main()
