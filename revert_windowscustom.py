#!/usr/bin/env python3
"""
WindowsCustom Revert Tool
Completely reverts all WindowsCustom modifications back to Windows defaults
"""

import winreg
import os
import subprocess
import time
import tkinter as tk
from tkinter import messagebox

class WindowsCustomRevert:
    def __init__(self):
        self.changes_found = False
        
    def check_for_changes(self):
        """Check if WindowsCustom modifications exist"""
        print("🔍 Checking for WindowsCustom modifications...")
        
        changes = []
        
        try:
            # Check registry modifications
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_READ) as key:
                try:
                    search_mode, _ = winreg.QueryValueEx(key, "SearchboxTaskbarMode")
                    if search_mode == 0:
                        changes.append("Search box hidden")
                except FileNotFoundError:
                    pass
                
                try:
                    small_icons, _ = winreg.QueryValueEx(key, "TaskbarSmallIcons")
                    if small_icons == 1:
                        changes.append("Small taskbar icons enabled")
                except FileNotFoundError:
                    pass
                
                try:
                    task_view, _ = winreg.QueryValueEx(key, "ShowTaskViewButton")
                    if task_view == 0:
                        changes.append("Task view button hidden")
                except FileNotFoundError:
                    pass
        except:
            pass
        
        # Check startup entry
        startup_path = os.path.join(os.getenv('APPDATA'), 
                                  'Microsoft', 'Windows', 'Start Menu', 
                                  'Programs', 'Startup')
        batch_file = os.path.join(startup_path, "WindowsCustom.bat")
        
        if os.path.exists(batch_file):
            changes.append("WindowsCustom startup entry")
        
        # Check WindowsCustom registry
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\WindowsCustom", 0, winreg.KEY_READ):
                changes.append("WindowsCustom registry entries")
        except FileNotFoundError:
            pass
        
        if changes:
            print(f"✅ Found {len(changes)} WindowsCustom modifications:")
            for change in changes:
                print(f"   • {change}")
            self.changes_found = True
        else:
            print("ℹ️ No WindowsCustom modifications found")
            self.changes_found = False
        
        return changes
    
    def revert_registry_changes(self):
        """Revert all registry modifications to Windows defaults"""
        print("🔧 Reverting registry changes...")
        
        try:
            # Revert taskbar settings
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                # Restore search box (Windows default: 1)
                winreg.SetValueEx(key, "SearchboxTaskbarMode", 0, winreg.REG_DWORD, 1)
                print("   ✅ Search box restored")
                
                # Restore normal taskbar icons (Windows default: 0)
                winreg.SetValueEx(key, "TaskbarSmallIcons", 0, winreg.REG_DWORD, 0)
                print("   ✅ Normal taskbar icons restored")
                
                # Restore task view button (Windows default: 1)
                winreg.SetValueEx(key, "ShowTaskViewButton", 0, winreg.REG_DWORD, 1)
                print("   ✅ Task view button restored")
                
                # Restore widgets button (Windows 11 default: 1)
                try:
                    winreg.SetValueEx(key, "TaskbarDa", 0, winreg.REG_DWORD, 1)
                    print("   ✅ Widgets button restored")
                except:
                    pass
                
                # Restore chat button (Windows 11 default: 1)
                try:
                    winreg.SetValueEx(key, "TaskbarMn", 0, winreg.REG_DWORD, 1)
                    print("   ✅ Chat button restored")
                except:
                    pass
            
            # Revert theme settings to Windows defaults
            theme_reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize"
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, theme_reg_path, 0, winreg.KEY_SET_VALUE) as key:
                    # Restore light theme (Windows default: 1)
                    winreg.SetValueEx(key, "SystemUsesLightTheme", 0, winreg.REG_DWORD, 1)
                    winreg.SetValueEx(key, "AppsUseLightTheme", 0, winreg.REG_DWORD, 1)
                    print("   ✅ Light theme restored")
            except:
                pass
            
            print("✅ All registry changes reverted to Windows defaults")
            return True
            
        except Exception as e:
            print(f"❌ Registry revert error: {e}")
            return False
    
    def remove_startup_entry(self):
        """Remove WindowsCustom startup entry"""
        print("🗑️ Removing startup entry...")
        
        try:
            startup_path = os.path.join(os.getenv('APPDATA'), 
                                      'Microsoft', 'Windows', 'Start Menu', 
                                      'Programs', 'Startup')
            
            batch_file = os.path.join(startup_path, "WindowsCustom.bat")
            
            if os.path.exists(batch_file):
                os.remove(batch_file)
                print("   ✅ WindowsCustom.bat removed from startup")
                return True
            else:
                print("   ℹ️ No startup entry found")
                return True
                
        except Exception as e:
            print(f"❌ Startup removal error: {e}")
            return False
    
    def cleanup_windowscustom_registry(self):
        """Clean up WindowsCustom registry entries"""
        print("🧹 Cleaning up WindowsCustom registry...")
        
        try:
            # Remove WindowsCustom registry key
            reg_path = r"SOFTWARE\WindowsCustom"
            try:
                winreg.DeleteKey(winreg.HKEY_CURRENT_USER, reg_path)
                print("   ✅ WindowsCustom registry key removed")
                return True
            except FileNotFoundError:
                print("   ℹ️ No WindowsCustom registry entries found")
                return True
                
        except Exception as e:
            print(f"❌ Registry cleanup error: {e}")
            return False
    
    def restart_explorer(self):
        """Restart Windows Explorer to apply changes"""
        print("🔄 Restarting Windows Explorer...")
        
        try:
            # Kill explorer
            subprocess.run(["taskkill", "/f", "/im", "explorer.exe"], capture_output=True)
            time.sleep(2)
            
            # Start explorer
            subprocess.Popen("explorer.exe")
            time.sleep(3)
            
            print("✅ Windows Explorer restarted")
            return True
            
        except Exception as e:
            print(f"❌ Explorer restart error: {e}")
            return False
    
    def perform_complete_revert(self):
        """Perform complete revert of all WindowsCustom changes"""
        print("🔄 REVERTING ALL WINDOWSCUSTOM CHANGES")
        print("=" * 45)
        
        success_count = 0
        total_steps = 4
        
        # Step 1: Revert registry changes
        if self.revert_registry_changes():
            success_count += 1
        
        # Step 2: Remove startup entry
        if self.remove_startup_entry():
            success_count += 1
        
        # Step 3: Clean up WindowsCustom registry
        if self.cleanup_windowscustom_registry():
            success_count += 1
        
        # Step 4: Restart Explorer
        if self.restart_explorer():
            success_count += 1
        
        print(f"\n📊 REVERT RESULTS: {success_count}/{total_steps} steps completed")
        
        if success_count == total_steps:
            print("\n🎉 COMPLETE REVERT SUCCESSFUL!")
            print("✅ Windows taskbar restored to default")
            print("✅ All WindowsCustom modifications removed")
            print("✅ System back to original state")
            return True
        else:
            print("\n⚠️ Partial revert completed")
            print("Some steps may need manual intervention")
            return False

def show_gui_revert():
    """Show GUI for revert confirmation"""
    root = tk.Tk()
    root.title("WindowsCustom Revert Tool")
    root.geometry("400x300")
    root.configure(bg="#2D2D2D")
    
    # Title
    tk.Label(root, text="WindowsCustom Revert Tool", 
            font=("Segoe UI", 16, "bold"),
            bg="#2D2D2D", fg="#FFFFFF").pack(pady=20)
    
    # Warning
    tk.Label(root, text="⚠️ WARNING ⚠️", 
            font=("Segoe UI", 12, "bold"),
            bg="#2D2D2D", fg="#FF6666").pack(pady=10)
    
    tk.Label(root, text="This will completely revert all WindowsCustom\nmodifications back to Windows defaults.", 
            font=("Segoe UI", 10),
            bg="#2D2D2D", fg="#FFFFFF").pack(pady=10)
    
    # What will be reverted
    tk.Label(root, text="What will be reverted:", 
            font=("Segoe UI", 10, "bold"),
            bg="#2D2D2D", fg="#FFFFFF").pack(pady=(20, 5))
    
    revert_items = [
        "• Search box restored",
        "• Task view button restored", 
        "• Normal taskbar icons restored",
        "• Widgets/Chat buttons restored",
        "• Light theme restored",
        "• Startup entry removed",
        "• WindowsCustom registry cleaned"
    ]
    
    for item in revert_items:
        tk.Label(root, text=item, 
                font=("Segoe UI", 9),
                bg="#2D2D2D", fg="#CCCCCC").pack(anchor="w", padx=50)
    
    # Buttons
    button_frame = tk.Frame(root, bg="#2D2D2D")
    button_frame.pack(pady=20)
    
    def confirm_revert():
        root.destroy()
        reverter = WindowsCustomRevert()
        reverter.perform_complete_revert()
    
    tk.Button(button_frame, text="🔄 Revert All Changes", 
             bg="#FF4444", fg="white", font=("Segoe UI", 10, "bold"),
             command=confirm_revert).pack(side="left", padx=10)
    
    tk.Button(button_frame, text="Cancel", 
             bg="#666666", fg="white", font=("Segoe UI", 10),
             command=root.destroy).pack(side="left", padx=10)
    
    root.mainloop()

def main():
    """Main function"""
    print("WindowsCustom Revert Tool")
    print("BY Aladdin Shenewa")
    print("=" * 30)
    
    reverter = WindowsCustomRevert()
    changes = reverter.check_for_changes()
    
    if not reverter.changes_found:
        print("\n✅ No WindowsCustom modifications found!")
        print("Your system is already in default state.")
        return
    
    print(f"\n⚠️ Found {len(changes)} WindowsCustom modifications")
    print("These will be reverted to Windows defaults.")
    
    # Ask for confirmation
    choice = input("\nProceed with revert? (y/n): ").lower().strip()
    
    if choice == 'y':
        reverter.perform_complete_revert()
    else:
        print("Revert cancelled")
        print("\n💡 You can also run the GUI version:")
        print("python revert_windowscustom.py --gui")

if __name__ == "__main__":
    import sys
    
    if "--gui" in sys.argv:
        show_gui_revert()
    else:
        main()
