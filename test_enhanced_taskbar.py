#!/usr/bin/env python3
"""
Test the enhanced Windows-style taskbar with all new features
"""

import subprocess
import time
import os

def test_features():
    """Test all taskbar features"""
    print("Enhanced Windows-Style Taskbar Test")
    print("=" * 50)
    print("BY Aladdin Shenewa")
    print()
    
    print("🎯 NEW FEATURES TO TEST:")
    print("✅ Windows-shaped UI with proper borders and styling")
    print("✅ Minimized apps SHOWN on taskbar (with different styling)")
    print("✅ Working start button with ALL APPS menu")
    print("✅ Music controls in taskbar (play/pause/stop)")
    print("✅ Search box (Windows style)")
    print("✅ Proper window management")
    print("✅ All buttons functional")
    print()
    
    print("🔧 TESTING COMPONENTS:")
    print()
    
    # Test start menu
    print("1. Testing Start Menu...")
    try:
        import start_menu
        print("   ✅ Start menu module loaded")
    except ImportError as e:
        print(f"   ❌ Start menu error: {e}")
    
    # Test music controls
    print("2. Testing Music Controls...")
    try:
        import music_controls
        controller = music_controls.init_music_controller()
        print("   ✅ Music controller initialized")
        
        # Test music functions
        status = controller.get_status()
        print(f"   📊 Music status: {status}")
        
    except ImportError as e:
        print(f"   ❌ Music controls error: {e}")
    
    # Test window management
    print("3. Testing Window Management...")
    try:
        import taskbar
        wm = taskbar.WindowManager()
        windows = wm.get_open_windows()
        print(f"   ✅ Found {len(windows)} open windows")
        
        # Show window states
        minimized_count = 0
        visible_count = 0
        
        for hwnd, info in windows.items():
            if info['minimized']:
                minimized_count += 1
            else:
                visible_count += 1
        
        print(f"   📊 Visible windows: {visible_count}")
        print(f"   📊 Minimized windows: {minimized_count}")
        print("   ✅ ALL windows (including minimized) will show on taskbar")
        
    except Exception as e:
        print(f"   ❌ Window management error: {e}")
    
    # Test theme system
    print("4. Testing Theme System...")
    try:
        import json
        if os.path.exists("settings.json"):
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            current_theme = settings.get("current_theme", "Unknown")
            print(f"   ✅ Current theme: {current_theme}")
            
            if current_theme in settings.get("themes", {}):
                theme = settings["themes"][current_theme]
                print(f"   🎨 Background: {theme['background']}")
                print(f"   🎨 Buttons: {theme['buttons']}")
                print(f"   🎨 Text: {theme['text']}")
        
    except Exception as e:
        print(f"   ❌ Theme system error: {e}")
    
    print()
    print("🚀 READY TO TEST!")
    print()

def demo_instructions():
    """Show demo instructions"""
    print("📋 HOW TO TEST THE ENHANCED TASKBAR:")
    print("=" * 45)
    print()
    
    print("1. 🖥️ START THE TASKBAR:")
    print("   python demo_taskbar.py")
    print("   (This will show the enhanced Windows-style taskbar)")
    print()
    
    print("2. 🎯 TEST FEATURES:")
    print("   • Click the ⊞ start button → Should open Windows-style menu")
    print("   • Click ▶️ play button → Should start music")
    print("   • Click ⏸️ pause button → Should pause music")
    print("   • Click ⏹️ stop button → Should stop music")
    print("   • Type in search box → Should work like Windows search")
    print("   • Open apps and minimize them → Should show as darker/sunken buttons")
    print("   • Click window buttons → Should restore/focus windows")
    print()
    
    print("3. 🎨 TEST THEMES:")
    print("   python settings_gui.py")
    print("   • Try Red theme and Hatsune Miku theme")
    print("   • Save settings and restart taskbar to see changes")
    print()
    
    print("4. 🎵 TEST MUSIC CONTROLS:")
    print("   • Make sure you have MP3 files in the 'music' folder")
    print("   • Use taskbar music buttons to control playback")
    print("   • Music should only play with headphones (if enabled)")
    print()
    
    print("5. 📱 TEST WINDOW MANAGEMENT:")
    print("   • Open Chrome, VSCode, Notepad, etc.")
    print("   • Minimize some apps → They should show as darker/sunken buttons")
    print("   • Restore apps → They should show as normal raised buttons")
    print()
    
    print("6. ⚙️ TEST START MENU:")
    print("   • Click start button")
    print("   • Try launching apps from the menu")
    print("   • Test music controls from start menu")
    print("   • Try power options")
    print()

def run_demo():
    """Run the demo"""
    choice = input("Start enhanced taskbar demo? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n🚀 Starting enhanced taskbar...")
        print("Press Ctrl+C in the taskbar window to exit")
        print("Windows taskbar will be restored automatically")
        print()
        
        try:
            subprocess.run(["python", "demo_taskbar.py"])
        except KeyboardInterrupt:
            print("\nDemo stopped by user")
        except Exception as e:
            print(f"Demo error: {e}")
    else:
        print("Demo cancelled")

def main():
    """Main test function"""
    test_features()
    demo_instructions()
    
    print("💡 TIP: Make sure you have:")
    print("   • MP3 files in 'music' folder for music controls")
    print("   • Some apps open to test window management")
    print("   • Administrator privileges for best results")
    print()
    
    run_demo()

if __name__ == "__main__":
    main()
