#!/usr/bin/env python3
"""
WindowsCustom Settings GUI
Comprehensive settings interface with color theme customization
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import json
import os

class SettingsGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("WindowsCustom Settings - BY Aladdin Shenewa")
        self.root.geometry("600x700")
        self.root.configure(bg="#2D2D2D")
        
        # Load current settings
        self.settings = self.load_settings()
        self.current_theme = self.settings.get("current_theme", "Hatsune Miku")
        
        self.create_widgets()
        
    def load_settings(self):
        """Load settings from JSON file"""
        if os.path.exists("settings.json"):
            with open("settings.json", "r") as f:
                return json.load(f)
        return {}
    
    def save_settings(self):
        """Save settings to JSON file"""
        try:
            with open("settings.json", "w") as f:
                json.dump(self.settings, f, indent=2)
            messagebox.showinfo("Success", "Settings saved successfully!")
            return True
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")
            return False
    
    def apply_theme(self, theme_name):
        """Apply a color theme"""
        if theme_name in self.settings.get("themes", {}):
            theme = self.settings["themes"][theme_name]
            self.settings["current_theme"] = theme_name
            self.settings["taskbar_colors"] = {
                "background": theme["background"],
                "buttons": theme["buttons"],
                "text": theme["text"],
                "accent": "#0078D4",
                "hover": "#404040"
            }
            self.current_theme = theme_name
            self.update_preview()
            
    def update_preview(self):
        """Update the color preview"""
        colors = self.settings.get("taskbar_colors", {})
        bg = colors.get("background", "#66BFF2")
        btn = colors.get("buttons", "#4DA6D9")
        txt = colors.get("text", "#15BFAE")
        
        # Update preview frame
        self.preview_frame.configure(bg=bg)
        self.preview_button.configure(bg=btn, fg=txt)
        self.preview_label.configure(bg=bg, fg=txt)
        
    def create_widgets(self):
        """Create the settings interface"""
        # Title
        title_label = tk.Label(self.root, text="WindowsCustom Settings", 
                              font=("Segoe UI", 18, "bold"), 
                              bg="#2D2D2D", fg="#FFFFFF")
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(self.root, text="BY Aladdin Shenewa", 
                                 font=("Segoe UI", 10), 
                                 bg="#2D2D2D", fg="#66BFF2")
        subtitle_label.pack(pady=5)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Theme tab
        theme_frame = tk.Frame(notebook, bg="#2D2D2D")
        notebook.add(theme_frame, text="Color Themes")
        self.create_theme_tab(theme_frame)
        
        # Features tab
        features_frame = tk.Frame(notebook, bg="#2D2D2D")
        notebook.add(features_frame, text="Features")
        self.create_features_tab(features_frame)
        
        # Advanced tab
        advanced_frame = tk.Frame(notebook, bg="#2D2D2D")
        notebook.add(advanced_frame, text="Advanced")
        self.create_advanced_tab(advanced_frame)
        
        # Buttons
        button_frame = tk.Frame(self.root, bg="#2D2D2D")
        button_frame.pack(fill="x", padx=10, pady=10)
        
        save_btn = tk.Button(button_frame, text="Save Settings", 
                            command=self.save_settings,
                            bg="#0078D4", fg="white", font=("Segoe UI", 10, "bold"))
        save_btn.pack(side="right", padx=5)
        
        cancel_btn = tk.Button(button_frame, text="Cancel", 
                              command=self.root.destroy,
                              bg="#666666", fg="white", font=("Segoe UI", 10))
        cancel_btn.pack(side="right", padx=5)
        
    def create_theme_tab(self, parent):
        """Create the theme customization tab"""
        # Theme selection
        theme_label = tk.Label(parent, text="Select Theme:", 
                              font=("Segoe UI", 12, "bold"),
                              bg="#2D2D2D", fg="#FFFFFF")
        theme_label.pack(anchor="w", padx=10, pady=10)
        
        # Theme buttons
        theme_frame = tk.Frame(parent, bg="#2D2D2D")
        theme_frame.pack(fill="x", padx=10, pady=5)
        
        # Red Theme
        red_btn = tk.Button(theme_frame, text="Red Theme", 
                           bg="#FF6B6B", fg="white", font=("Segoe UI", 10, "bold"),
                           command=lambda: self.apply_theme("Red"))
        red_btn.pack(side="left", padx=5, pady=5)
        
        # Hatsune Miku Theme
        miku_btn = tk.Button(theme_frame, text="Hatsune Miku Theme", 
                            bg="#66BFF2", fg="#15BFAE", font=("Segoe UI", 10, "bold"),
                            command=lambda: self.apply_theme("Hatsune Miku"))
        miku_btn.pack(side="left", padx=5, pady=5)
        
        # Windows Dark Theme
        dark_btn = tk.Button(theme_frame, text="Windows Dark", 
                            bg="#1F1F1F", fg="white", font=("Segoe UI", 10, "bold"),
                            command=lambda: self.apply_theme("Windows Dark"))
        dark_btn.pack(side="left", padx=5, pady=5)
        
        # Current theme indicator
        current_label = tk.Label(parent, text=f"Current Theme: {self.current_theme}", 
                                font=("Segoe UI", 10),
                                bg="#2D2D2D", fg="#66BFF2")
        current_label.pack(anchor="w", padx=10, pady=5)
        
        # Preview section
        preview_label = tk.Label(parent, text="Preview:", 
                                font=("Segoe UI", 12, "bold"),
                                bg="#2D2D2D", fg="#FFFFFF")
        preview_label.pack(anchor="w", padx=10, pady=(20, 5))
        
        # Preview frame
        self.preview_frame = tk.Frame(parent, height=80, relief="raised", bd=2)
        self.preview_frame.pack(fill="x", padx=10, pady=5)
        self.preview_frame.pack_propagate(False)
        
        self.preview_button = tk.Button(self.preview_frame, text="Sample Button", 
                                       font=("Segoe UI", 10))
        self.preview_button.pack(side="left", padx=10, pady=10)
        
        self.preview_label = tk.Label(self.preview_frame, text="Sample Text | 12:34 | 🔋 85%", 
                                     font=("Segoe UI", 10))
        self.preview_label.pack(side="right", padx=10, pady=10)
        
        # Update preview with current theme
        self.update_preview()
        
    def create_features_tab(self, parent):
        """Create the features tab"""
        features_label = tk.Label(parent, text="Enable/Disable Features:", 
                                 font=("Segoe UI", 12, "bold"),
                                 bg="#2D2D2D", fg="#FFFFFF")
        features_label.pack(anchor="w", padx=10, pady=10)
        
        # Feature checkboxes
        self.feature_vars = {}
        
        features = [
            ("run_on_start", "Run on Windows startup"),
            ("enable_music", "Music player"),
            ("enable_backup", "Smart backup system"),
            ("fake_bsod_enabled", "Fake BSOD on low battery"),
            ("headphones_only_music", "Music only with headphones"),
            ("keyboard_light_enabled", "Keyboard lighting"),
            ("daily_trash_cleanup_enabled", "Daily trash cleanup")
        ]
        
        for key, label in features:
            var = tk.BooleanVar(value=self.settings.get(key, True))
            self.feature_vars[key] = var
            
            cb = tk.Checkbutton(parent, text=label, variable=var,
                               bg="#2D2D2D", fg="#FFFFFF", 
                               selectcolor="#66BFF2",
                               font=("Segoe UI", 10))
            cb.pack(anchor="w", padx=20, pady=2)
            
    def create_advanced_tab(self, parent):
        """Create the advanced settings tab"""
        advanced_label = tk.Label(parent, text="Advanced Settings:", 
                                 font=("Segoe UI", 12, "bold"),
                                 bg="#2D2D2D", fg="#FFFFFF")
        advanced_label.pack(anchor="w", padx=10, pady=10)
        
        # Timer settings
        timer_frame = tk.Frame(parent, bg="#2D2D2D")
        timer_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(timer_frame, text="Work Timer (minutes):", 
                bg="#2D2D2D", fg="#FFFFFF").pack(side="left")
        
        self.work_timer_var = tk.StringVar(value=str(self.settings.get("break_timer_minutes", 59)))
        work_entry = tk.Entry(timer_frame, textvariable=self.work_timer_var, width=10)
        work_entry.pack(side="left", padx=10)
        
        tk.Label(timer_frame, text="Break Duration (minutes):", 
                bg="#2D2D2D", fg="#FFFFFF").pack(side="left", padx=(20, 0))
        
        self.break_timer_var = tk.StringVar(value=str(self.settings.get("break_duration_minutes", 20)))
        break_entry = tk.Entry(timer_frame, textvariable=self.break_timer_var, width=10)
        break_entry.pack(side="left", padx=10)
        
        # Font size
        font_frame = tk.Frame(parent, bg="#2D2D2D")
        font_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(font_frame, text="Minimum Font Size:", 
                bg="#2D2D2D", fg="#FFFFFF").pack(side="left")
        
        self.font_size_var = tk.StringVar(value=str(self.settings.get("min_font_size", 15)))
        font_entry = tk.Entry(font_frame, textvariable=self.font_size_var, width=10)
        font_entry.pack(side="left", padx=10)
        
    def save_settings(self):
        """Save all settings"""
        # Update feature settings
        for key, var in self.feature_vars.items():
            self.settings[key] = var.get()
            
        # Update advanced settings
        try:
            self.settings["break_timer_minutes"] = int(self.work_timer_var.get())
            self.settings["break_duration_minutes"] = int(self.break_timer_var.get())
            self.settings["min_font_size"] = int(self.font_size_var.get())
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for timer and font settings")
            return
            
        # Save to file
        if self.save_settings():
            self.root.destroy()
    
    def run(self):
        """Run the settings GUI"""
        self.root.mainloop()

def open_settings():
    """Function to open settings GUI"""
    settings_gui = SettingsGUI()
    settings_gui.run()

if __name__ == "__main__":
    open_settings()
