#!/usr/bin/env python3
"""
WindowsCustom Launcher
Launches the WindowsCustom system with proper error handling and logging
"""

import sys
import os
import time
import traceback
import threading
from datetime import datetime

def log_message(message):
    """Log message with timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_dependencies():
    """Check if all required dependencies are available"""
    log_message("Checking dependencies...")
    
    required_modules = [
        'tkinter', 'pygame', 'psutil', 'requests', 'json', 'threading'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        log_message(f"Missing dependencies: {', '.join(missing)}")
        return False
    
    log_message("All dependencies available ✓")
    return True

def check_files():
    """Check if all required files exist"""
    log_message("Checking required files...")
    
    required_files = [
        'main.py', 'taskbar.py', 'music_player.py', 'backup.py', 
        'utils.py', 'settings.json', 'splash_screen.py', 'edge_blocker.py'
    ]
    
    missing = []
    for file in required_files:
        if not os.path.exists(file):
            missing.append(file)
    
    if missing:
        log_message(f"Missing files: {', '.join(missing)}")
        return False
    
    log_message("All required files found ✓")
    return True

def launch_system():
    """Launch the WindowsCustom system"""
    log_message("Starting WindowsCustom System...")
    log_message("=" * 50)
    
    try:
        # Import main after checks
        import main
        
        # Run the main function
        main.main()
        
    except KeyboardInterrupt:
        log_message("System stopped by user")
    except Exception as e:
        log_message(f"System error: {e}")
        traceback.print_exc()
        return False
    
    return True

def show_features():
    """Show what features are being loaded"""
    log_message("WindowsCustom Features:")
    log_message("- Boot splash screen with Windows logo")
    log_message("- Custom taskbar (Chrome-style system tray)")
    log_message("- Battery/Calendar/Time grouped display")
    log_message("- Most-used apps tracking and display")
    log_message("- Microsoft Edge blocking system")
    log_message("- Music player with headphone detection")
    log_message("- Smart backup system")
    log_message("- Break timer (59min work / 20min break)")
    log_message("- Fake BSOD on low battery")
    log_message("- Temp folder cleanup")
    log_message("- Daily trash management")
    log_message("- Cybernews integration")
    log_message("- Screen recording widget")
    log_message("- Settings GUI with color customization")
    log_message("")

def main():
    """Main launcher function"""
    print("WindowsCustom System Launcher")
    print("=" * 40)
    print("BY Aladdin Shenewa")
    print("")
    
    # Show features
    show_features()
    
    # Check dependencies
    if not check_dependencies():
        log_message("Please install missing dependencies with:")
        log_message("pip install pygame psutil requests")
        return False
    
    # Check files
    if not check_files():
        log_message("Please ensure all required files are present")
        return False
    
    # Launch system
    log_message("All checks passed, launching system...")
    time.sleep(1)
    
    return launch_system()

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            log_message("System failed to start properly")
            sys.exit(1)
    except KeyboardInterrupt:
        log_message("Launcher interrupted by user")
        sys.exit(0)
    except Exception as e:
        log_message(f"Launcher error: {e}")
        traceback.print_exc()
        sys.exit(1)
