# WindowsCustom - C# WPF Edition
**BY Aladdin <PERSON>**

## 🎯 **COMPLETE REWRITE IN C#**

I've completely scrapped the Python approach and rebuilt WindowsCustom in **C# with WPF** for much better Windows integration and native UI performance.

## ✅ **WHY C# IS BETTER**

### **Native Windows Integration:**
- ✅ **True Windows API access** - no Python limitations
- ✅ **Native WPF UI** - looks and feels like real Windows apps
- ✅ **Better taskbar management** - proper Windows shell integration
- ✅ **Hardware acceleration** - smooth animations and rendering
- ✅ **Lower resource usage** - compiled native code

### **Professional UI:**
- ✅ **XAML-based interface** - professional Windows styling
- ✅ **Proper theming system** - dynamic resource binding
- ✅ **Smooth animations** - native WPF transitions
- ✅ **Responsive design** - adapts to different screen sizes
- ✅ **Native controls** - buttons, menus, dialogs that feel right

## 🚀 **HOW TO BUILD & RUN**

### **Prerequisites:**
- **Windows 10/11**
- **.NET 6.0 Desktop Runtime** ([Download here](https://dotnet.microsoft.com/download/dotnet/6.0))

### **Build:**
```bash
build.bat
```

### **Run:**
```bash
cd bin\Release\net6.0-windows
WindowsCustom.exe
```

## 🎨 **FEATURES**

### **🖥️ Custom Taskbar:**
- **Replaces Windows taskbar** with custom WPF interface
- **Full-width themed background** - your colors fill the entire taskbar
- **Native Windows feel** - proper window management
- **App switching buttons** - shows running applications
- **System tray integration** - clock and system info

### **🎵 Music Player:**
- **NAudio-powered** - professional audio library
- **Full music controls** - Play, Pause, Stop, Next, Previous
- **Playlist support** - loads from `music/` folder
- **Multiple formats** - MP3, WAV, FLAC, M4A
- **Auto-advance** - plays next track automatically

### **🎨 Advanced Theming:**
- **5 Built-in themes:**
  - **Hatsune Miku** - Signature teal and blue
  - **Red** - Bold red styling
  - **Windows Dark** - Modern dark theme
  - **Ocean** - Blue ocean colors
  - **Purple** - Rich purple tones
- **Live theme switching** - instant visual updates
- **Theme preview** - see before you apply

### **⚙️ Settings System:**
- **Tabbed interface** - organized settings
- **General settings** - startup, notifications
- **Music settings** - folder selection, playback options
- **Appearance settings** - taskbar height, visual options

### **📊 System Stats:**
- **Real-time monitoring** - CPU, memory, disk usage
- **Process count** - running applications
- **System uptime** - how long Windows has been running
- **OS information** - version and .NET details

## 🎯 **ARCHITECTURE**

### **Core Classes:**
- **MainWindow** - Main taskbar interface
- **TaskbarManager** - Windows taskbar control
- **MusicPlayer** - Audio playback system
- **ThemeManager** - Dynamic theming
- **SettingsWindow** - Configuration interface
- **SystemStatsWindow** - Performance monitoring

### **Technology Stack:**
- **WPF (Windows Presentation Foundation)** - UI framework
- **NAudio** - Audio processing
- **System.Management** - Windows system info
- **Newtonsoft.Json** - Configuration storage

## 🎨 **VISUAL COMPARISON**

### **Python Version Issues:**
- ❌ Overlay windows that don't integrate
- ❌ Tkinter looks outdated
- ❌ Performance issues
- ❌ Limited Windows API access
- ❌ Glitchy behavior

### **C# WPF Version:**
- ✅ **Native Windows integration**
- ✅ **Modern, professional UI**
- ✅ **Smooth performance**
- ✅ **Full Windows API access**
- ✅ **Reliable operation**

## 🛡️ **SAFETY**

### **Safe Operations:**
- ✅ **Graceful taskbar hiding/showing**
- ✅ **Automatic restoration on exit**
- ✅ **Single instance protection**
- ✅ **Error handling throughout**

### **Easy Restoration:**
- **🔄 Restore button** - instant Windows taskbar restoration
- **Alt+F4** - closes app and restores taskbar
- **Task Manager** - kill process to restore taskbar

## 📁 **PROJECT STRUCTURE**

```
WindowsCustom/
├── WindowsCustom.csproj      # Project file
├── App.xaml                  # Application definition
├── App.xaml.cs              # Application logic
├── MainWindow.xaml          # Main taskbar UI
├── MainWindow.xaml.cs       # Main taskbar logic
├── TaskbarManager.cs        # Windows taskbar control
├── MusicPlayer.cs           # Audio system
├── ThemeManager.cs          # Theming system
├── ThemeSelector.xaml       # Theme selection UI
├── SettingsWindow.xaml      # Settings interface
├── SystemStatsWindow.xaml   # Stats display
├── build.bat               # Build script
└── music/                  # Music files folder
```

## 🎉 **RESULT**

**You now have a professional, native Windows application that:**
- 🎨 **Completely replaces the Windows taskbar** with your custom themes
- 🎵 **Includes a full-featured music player**
- ⚙️ **Has comprehensive settings and theming**
- 📊 **Shows system statistics**
- 🛡️ **Operates safely with easy restoration**
- ✅ **Looks and feels like a real Windows application**

**This is a complete, professional solution built with the right technology for Windows customization!**

---

## 🚀 **GET STARTED**

1. **Run:** `build.bat`
2. **Execute:** `bin\Release\net6.0-windows\WindowsCustom.exe`
3. **Enjoy your completely customized Windows taskbar!**
