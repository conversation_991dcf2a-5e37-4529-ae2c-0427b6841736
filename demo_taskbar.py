#!/usr/bin/env python3
"""
Demo script to show the enhanced Windows-themed taskbar with window management
"""

import json
import os
import time
import threading

def load_settings():
    """Load settings from JSON file"""
    if os.path.exists("settings.json"):
        with open("settings.json", "r") as f:
            return json.load(f)
    return {}

def demo_taskbar():
    """Demo the enhanced taskbar"""
    print("WindowsCustom Enhanced Taskbar Demo")
    print("=" * 50)
    print("BY Aladdin Shenewa")
    print()
    
    print("🎨 NEW FEATURES:")
    print("✓ Windows 11 themed styling")
    print("✓ Window management - minimized apps hidden from taskbar")
    print("✓ Real-time window state updates")
    print("✓ Click window buttons to restore/minimize")
    print("✓ Authentic Windows colors and styling")
    print()
    
    print("🎯 TASKBAR LAYOUT:")
    print("┌─ Start ⊞ ─ Weather 🌤️ ─┬─ Window Buttons ─┬─ Apps 💻🌐📁 ─┬─ System Panel ─┐")
    print("│                        │                  │                │   🔋 31%      │")
    print("│                        │ Chrome 🌐        │                │   00:57       │")
    print("│                        │ VSCode 💻        │                │   THU/JUN     │")
    print("│                        │ Explorer 📁      │                │               │")
    print("└────────────────────────┴──────────────────┴────────────────┴───────────────┘")
    print()
    
    # Load settings
    settings = load_settings()
    colors = settings.get("taskbar_colors", {})
    
    print("🎨 CURRENT THEME:")
    print(f"   Background: {colors.get('background', '#1F1F1F')} (Windows 11 Dark)")
    print(f"   Buttons:    {colors.get('buttons', '#2D2D2D')} (Windows Button Style)")
    print(f"   Text:       {colors.get('text', '#FFFFFF')} (White Text)")
    print(f"   Accent:     {colors.get('accent', '#0078D4')} (Windows Blue)")
    print()
    
    print("🚀 STARTING ENHANCED TASKBAR...")
    print("   - Hiding Windows taskbar")
    print("   - Creating custom Windows-themed taskbar")
    print("   - Enabling window management")
    print("   - Starting real-time updates")
    print()
    
    print("💡 USAGE:")
    print("   • Click window buttons to minimize/restore apps")
    print("   • Minimized apps will be hidden from taskbar")
    print("   • System info updates every 3 seconds")
    print("   • Press Ctrl+C to exit and restore Windows taskbar")
    print()
    
    input("Press Enter to start the enhanced taskbar...")
    
    try:
        # Import and start taskbar
        import taskbar
        print("Starting taskbar...")
        taskbar.init_taskbar(settings)
        
    except KeyboardInterrupt:
        print("\nTaskbar stopped by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_taskbar()
