# WindowsCustom BY Aladdin Shenewa

A comprehensive Windows customization system that transforms your Windows experience with custom taskbar, music player, security features, and productivity tools.

## 🚀 Features Implemented

### ✅ Core System
- **Boot Splash Screen**: Grey screen with Windows logo and "WindowsCustom BY Aladdin Shenewa"
- **Enhanced Taskbar**: Chrome-style taskbar with grouped battery/calendar/time
- **Microsoft Edge Blocking**: Automatically blocks Edge and redirects to Chrome
- **Most-Used Apps Tracking**: Tracks and displays your most frequently used applications

### ✅ Music System
- **Headphone-Only Playback**: Music only plays when headphones are connected
- **Smart Media Pause**: Automatically pauses when other media (YouTube, etc.) is playing
- **Folder Playback**: Plays music from folder alphabetically (top to bottom)
- **Real-time Audio Detection**: Uses Windows audio APIs to detect headphones

### ✅ Smart Backup System
- **Intelligent File Selection**: Only backs up unique files, skips common applications
- **Cloud Integration**: Automatically syncs to OneDrive
- **Duplicate Prevention**: Checks file modification times before backing up
- **Background Operation**: Runs silently in the background

### ✅ Productivity Features
- **Break Timer**: 59-minute work sessions with 20-minute break screens
- **Temp Folder Cleanup**: Cleans temporary files on startup
- **Battery Management**: Shows fake BSOD at 10% battery until charger connected
- **Daily Trash Cleanup**: Automatically manages trash folder

### ✅ Security Features
- **Download Scanning**: Monitors and scans suspicious downloads
- **Cookie Management**: Cleans old cookies while preserving login sessions
- **Site Safety**: Warns about sketchy downloads with multiple confirmations

### ✅ Developer Integration
- **VSCode Integration**: Auto-error fixing, keyboard lighting, font scaling
- **Library Auto-Install**: Automatically installs Python libraries when detected
- **Screen Recording**: Integrated snipping tool for video creation

### ✅ Customization
- **Color Themes**: Customizable colors for taskbar, buttons, and text
- **Settings GUI**: Comprehensive settings interface
- **Feature Toggles**: Enable/disable any feature individually

## 📁 File Structure

```
customUI/
├── main.py                 # Main application entry point
├── launch_windowscustom.py # Enhanced launcher with logging
├── splash_screen.py        # Boot splash screen
├── taskbar.py             # Custom taskbar implementation
├── music_player.py        # Music system with headphone detection
├── edge_blocker.py        # Microsoft Edge blocking system
├── backup.py              # Smart backup system
├── utils.py               # Utility functions
├── settings.json          # Configuration file
├── test_system.py         # System testing script
├── test_taskbar.py        # Taskbar testing script
├── music/                 # Music folder
│   ├── rainyBoot.mp3
│   └── Andrew Applepie & Burjman - Arrow.mp3
└── assets/                # Assets folder (for future icons)
```

## 🚀 How to Run

### Method 1: Enhanced Launcher (Recommended)
```bash
python launch_windowscustom.py
```

### Method 2: Direct Launch
```bash
python main.py
```

### Method 3: Test Individual Components
```bash
python test_system.py      # Test all components
python test_taskbar.py     # Test taskbar only
```

## ⚙️ Configuration

Edit `settings.json` to customize your experience:

```json
{
  "run_on_start": true,
  "enable_music": true,
  "enable_backup": true,
  "fake_bsod_enabled": true,
  "headphones_only_music": true,
  "keyboard_light_enabled": true,
  "min_font_size": 15,
  "break_timer_minutes": 59,
  "break_duration_minutes": 20,
  "taskbar_colors": {
    "background": "#202020",
    "buttons": "#404040", 
    "text": "#FFFFFF"
  }
}
```

## 🔧 Dependencies

All required dependencies are automatically checked and should be installed:

```bash
pip install pygame psutil requests
```

## 🎯 Key Features Explained

### Custom Taskbar
- **Chrome-style System Tray**: Battery, time, and date grouped together
- **Start Button**: Moved to far left corner next to weather
- **Most-Used Apps**: Dynamically shows your most frequently used applications
- **No Search Bar**: Removed Windows search, more space for apps
- **Real-time Updates**: Battery and time update every 5 seconds

### Music Player
- **Headphone Detection**: Uses PowerShell to detect audio device types
- **Media Conflict Resolution**: Pauses when browsers or media players are active
- **Alphabetical Playback**: Plays files in sorted order from music folder
- **Background Operation**: Runs as daemon thread

### Edge Blocking
- **Process Monitoring**: Continuously monitors for Edge processes
- **Registry Modification**: Sets Chrome as default browser
- **Startup Prevention**: Removes Edge from startup entries
- **Link Redirection**: Redirects Edge links to Chrome

## 🚨 Important Notes

1. **Administrator Rights**: Some features (like hiding Windows taskbar) may require administrator privileges
2. **Windows Only**: This system is designed specifically for Windows
3. **Antivirus**: Some antivirus software may flag the Edge blocking features
4. **Backup**: The system creates backups before making system changes

## 🐛 Troubleshooting

If the system doesn't start:
1. Run `python test_system.py` to check for issues
2. Ensure all dependencies are installed
3. Check that `settings.json` is valid JSON
4. Try running with administrator privileges

## 🔄 Auto-Start Setup

To run WindowsCustom on boot:
1. Press `Win + R`, type `shell:startup`
2. Create a shortcut to `launch_windowscustom.py`
3. Or use Task Scheduler for more control

## 📝 Customization

The system is highly modular. You can:
- Modify colors in `settings.json`
- Add new apps to the taskbar
- Change music folder location
- Adjust timer intervals
- Enable/disable any feature

## 🎨 Color Customization

Customize the appearance by editing the `taskbar_colors` section in `settings.json`:
- `background`: Main taskbar background color
- `buttons`: Button background color  
- `text`: Text color for labels

## 🔒 Security Features

- Download scanning with user confirmation
- Cookie cleanup for unused sites
- Automatic library installation with safety checks
- Multiple confirmation dialogs for risky actions

---

**WindowsCustom BY Aladdin Shenewa** - Making Windows more personal and productive!
