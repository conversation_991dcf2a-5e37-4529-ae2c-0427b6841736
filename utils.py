import json
import os
import subprocess
import platform
import psutil
from pathlib import Path

SETTINGS_FILE = "settings.json"

def load_settings() -> dict:
    """Load settings from the JSON file or return defaults."""
    if not os.path.isfile(SETTINGS_FILE):
        # Default settings
        return {
            "run_on_start": True,
            "enable_music": True,
            "enable_backup": True,
            "fake_bsod_enabled": True,
            "most_used_apps": {},  # Could store usage counts
        }
    try:
        with open(SETTINGS_FILE, "r") as f:
            return json.load(f)
    except Exception as e:
        print(f"[utils] Error loading settings: {e}")
        return {}

def save_settings(settings: dict):
    """Save settings dictionary to JSON file."""
    try:
        with open(SETTINGS_FILE, "w") as f:
            json.dump(settings, f, indent=2)
    except Exception as e:
        print(f"[utils] Error saving settings: {e}")

def launch_app(app_path: str) -> bool:
    """
    Try to launch an application or file.
    Returns True on success, False otherwise.
    """
    try:
        if platform.system() == "Windows":
            os.startfile(app_path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.Popen(["open", app_path])
        else:  # Linux and others
            subprocess.Popen([app_path])
        return True
    except Exception as e:
        print(f"[utils] Failed to launch {app_path}: {e}")
        return False

def get_most_used_apps(limit: int = 5) -> list:
    """
    Placeholder: Return a list of most used apps.
    For a full solution, you'd need usage stats from OS or logs.
    Here, return a fixed list for demonstration.
    """
    # Example structure: list of dict {name, path, usage_count}
    apps = [
        {"name": "Chrome", "path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "usage_count": 120},
        {"name": "VSCode", "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "usage_count": 90},
        {"name": "File Explorer", "path": "explorer.exe", "usage_count": 80},
        {"name": "Notepad", "path": "notepad.exe", "usage_count": 70},
        {"name": "Calculator", "path": "calc.exe", "usage_count": 60},
    ]
    return apps[:limit]

def cleanup_temp_files(temp_dirs: list = None):
    """
    Delete files from temp directories.
    temp_dirs - list of folders to clean, default to system temp.
    """
    if temp_dirs is None:
        temp_dirs = [os.getenv("TEMP"), os.getenv("TMP")]
    for temp_dir in temp_dirs:
        if not temp_dir or not os.path.exists(temp_dir):
            continue
        try:
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                    except Exception:
                        pass
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    try:
                        os.rmdir(dir_path)
                    except Exception:
                        pass
        except Exception as e:
            print(f"[utils] Error cleaning temp files in {temp_dir}: {e}")

def get_system_info() -> dict:
    """Return basic system info dictionary."""
    info = {
        "platform": platform.system(),
        "platform_version": platform.version(),
        "platform_release": platform.release(),
        "architecture": platform.machine(),
        "processor": platform.processor(),
        "cpu_count": psutil.cpu_count(logical=True),
        "total_memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "disk_total_gb": round(psutil.disk_usage('/').total / (1024**3), 2),
        "disk_free_gb": round(psutil.disk_usage('/').free / (1024**3), 2),
    }
    return info

def list_music_files(music_folder="music") -> list:
    """
    List mp3 and wav files from music folder.
    """
    p = Path(music_folder)
    if not p.exists():
        return []
    return [str(f) for f in p.glob("*.mp3")] + [str(f) for f in p.glob("*.wav")]

def backup_files(source_dirs: list, backup_dir: str):
    """
    Placeholder for backup logic.
    Copies files from source_dirs to backup_dir.
    """
    import shutil
    for source in source_dirs:
        if not os.path.exists(source):
            continue
        dest = os.path.join(backup_dir, os.path.basename(source))
        try:
            if os.path.isdir(source):
                shutil.copytree(source, dest, dirs_exist_ok=True)
            else:
                shutil.copy2(source, dest)
        except Exception as e:
            print(f"[utils] Backup failed for {source}: {e}")

def fake_bsod_trigger():
    """
    This function can simulate a fake BSOD by showing a full screen blue window.
    This is a placeholder; real implementation goes into UI module.
    """
    pass

if __name__ == "__main__":
    # Quick test
    print("Loaded settings:", load_settings())
    print("System Info:", get_system_info())
    print("Music files:", list_music_files())
    print("Most used apps:", get_most_used_apps())
