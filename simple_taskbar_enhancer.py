#!/usr/bin/env python3
"""
Simple Windows Taskbar Enhancer
Adds music controls and theme support to existing Windows taskbar
"""

import tkinter as tk
import win32gui
import json
import os
import subprocess
from datetime import datetime

class SimpleTaskbarEnhancer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # Hide main window
        self.overlays = []
        
    def get_taskbar_rect(self):
        """Get Windows taskbar position"""
        try:
            taskbar = win32gui.FindWindow("Shell_TrayWnd", None)
            if taskbar:
                rect = win32gui.GetWindowRect(taskbar)
                return {
                    'left': rect[0], 'top': rect[1],
                    'right': rect[2], 'bottom': rect[3],
                    'width': rect[2] - rect[0],
                    'height': rect[3] - rect[1]
                }
        except:
            pass
        
        # Fallback - assume taskbar at bottom
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        return {
            'left': 0, 'top': screen_height - 40,
            'right': screen_width, 'bottom': screen_height,
            'width': screen_width, 'height': 40
        }
    
    def load_theme(self):
        """Load current theme colors"""
        try:
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            theme_name = settings.get("current_theme", "Hatsune Miku")
            themes = settings.get("themes", {})
            
            if theme_name in themes:
                return themes[theme_name]
        except:
            pass
        
        # Default theme
        return {
            "background": "#66BFF2",
            "buttons": "#39C5BB",
            "text": "#1E3A8A"
        }
    
    def create_music_overlay(self):
        """Create music controls overlay"""
        taskbar = self.get_taskbar_rect()
        theme = self.load_theme()
        
        # Create overlay window
        overlay = tk.Toplevel(self.root)
        overlay.overrideredirect(True)
        overlay.configure(bg=theme["background"])
        overlay.wm_attributes("-topmost", True)
        
        # Position after start button
        width = 100
        height = taskbar['height'] - 6
        x = taskbar['left'] + 250  # After start and search
        y = taskbar['top'] + 3
        
        overlay.geometry(f"{width}x{height}+{x}+{y}")
        
        # Music controls
        frame = tk.Frame(overlay, bg=theme["background"])
        frame.pack(fill="both", expand=True, padx=2, pady=2)
        
        # Control buttons
        tk.Button(frame, text="▶️", font=("Segoe UI", 8), width=2,
                 bg=theme["buttons"], fg=theme["text"],
                 command=self.play_music).pack(side="left", padx=1)
        
        tk.Button(frame, text="⏸️", font=("Segoe UI", 8), width=2,
                 bg=theme["buttons"], fg=theme["text"],
                 command=self.pause_music).pack(side="left", padx=1)
        
        tk.Button(frame, text="⏹️", font=("Segoe UI", 8), width=2,
                 bg=theme["buttons"], fg=theme["text"],
                 command=self.stop_music).pack(side="left", padx=1)
        
        self.overlays.append(overlay)
        print("🎵 Music controls added to taskbar")
    
    def create_settings_overlay(self):
        """Create settings overlay"""
        taskbar = self.get_taskbar_rect()
        theme = self.load_theme()
        
        # Create overlay window
        overlay = tk.Toplevel(self.root)
        overlay.overrideredirect(True)
        overlay.configure(bg=theme["background"])
        overlay.wm_attributes("-topmost", True)
        
        # Position before system tray
        width = 120
        height = taskbar['height'] - 6
        x = taskbar['right'] - width - 100  # Before system tray
        y = taskbar['top'] + 3
        
        overlay.geometry(f"{width}x{height}+{x}+{y}")
        
        # Settings controls
        frame = tk.Frame(overlay, bg=theme["background"])
        frame.pack(fill="both", expand=True, padx=2, pady=2)
        
        # Settings button
        tk.Button(frame, text="⚙️", font=("Segoe UI", 8), width=2,
                 bg=theme["buttons"], fg=theme["text"],
                 command=self.open_settings).pack(side="left", padx=1)
        
        # Theme button
        tk.Button(frame, text="🎨", font=("Segoe UI", 8), width=2,
                 bg=theme["buttons"], fg=theme["text"],
                 command=self.quick_theme).pack(side="left", padx=1)
        
        # Time display
        time_label = tk.Label(frame, text=datetime.now().strftime("%H:%M"),
                             font=("Segoe UI", 8),
                             bg=theme["buttons"], fg=theme["text"])
        time_label.pack(side="right", padx=2)
        
        # Update time
        def update_time():
            time_label.config(text=datetime.now().strftime("%H:%M"))
            time_label.after(60000, update_time)  # Update every minute
        
        update_time()
        
        self.overlays.append(overlay)
        print("⚙️ Settings controls added to taskbar")
    
    def play_music(self):
        """Play music"""
        try:
            import music_controls
            music_controls.play_music()
            print("▶️ Music started")
        except Exception as e:
            print(f"Music error: {e}")
    
    def pause_music(self):
        """Pause music"""
        try:
            import music_controls
            music_controls.pause_music()
            print("⏸️ Music paused")
        except Exception as e:
            print(f"Music error: {e}")
    
    def stop_music(self):
        """Stop music"""
        try:
            import music_controls
            music_controls.stop_music()
            print("⏹️ Music stopped")
        except Exception as e:
            print(f"Music error: {e}")
    
    def open_settings(self):
        """Open settings"""
        try:
            subprocess.Popen(["python", "settings_gui.py"])
            print("⚙️ Settings opened")
        except Exception as e:
            print(f"Settings error: {e}")
    
    def quick_theme(self):
        """Quick theme selector"""
        popup = tk.Toplevel(self.root)
        popup.title("Quick Theme")
        popup.geometry("150x100")
        popup.configure(bg="#2D2D2D")
        popup.wm_attributes("-topmost", True)
        
        tk.Label(popup, text="Select Theme", bg="#2D2D2D", fg="white").pack(pady=5)
        
        tk.Button(popup, text="🔴 Red", bg="#FF6B6B", fg="white",
                 command=lambda: self.apply_theme("Red", popup)).pack(pady=1)
        
        tk.Button(popup, text="💙 Miku", bg="#66BFF2", fg="#1E3A8A",
                 command=lambda: self.apply_theme("Hatsune Miku", popup)).pack(pady=1)
        
        tk.Button(popup, text="⚫ Dark", bg="#1F1F1F", fg="white",
                 command=lambda: self.apply_theme("Windows Dark", popup)).pack(pady=1)
    
    def apply_theme(self, theme_name, popup):
        """Apply theme"""
        try:
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            settings["current_theme"] = theme_name
            
            with open("settings.json", "w") as f:
                json.dump(settings, f, indent=2)
            
            popup.destroy()
            print(f"🎨 Applied {theme_name} theme")
            
            # Restart to apply new theme
            self.restart()
            
        except Exception as e:
            print(f"Theme error: {e}")
    
    def restart(self):
        """Restart overlays"""
        self.stop()
        self.root.after(1000, self.start)  # Restart after 1 second
    
    def start(self):
        """Start the taskbar enhancer"""
        print("WindowsCustom Taskbar Enhancer")
        print("=" * 35)
        print("Adding enhancements to Windows taskbar...")
        
        try:
            self.create_music_overlay()
            self.create_settings_overlay()
            
            print("\n✅ Taskbar enhanced successfully!")
            print("🎵 Music controls: ▶️⏸️⏹️")
            print("⚙️ Settings: ⚙️🎨")
            print("\nPress Ctrl+C to stop")
            
        except Exception as e:
            print(f"❌ Enhancement failed: {e}")
    
    def stop(self):
        """Stop all overlays"""
        for overlay in self.overlays:
            try:
                if overlay.winfo_exists():
                    overlay.destroy()
            except (tk.TclError, AttributeError):
                # Overlay already destroyed or doesn't exist
                pass
        self.overlays.clear()
        print("🛑 Taskbar enhancements removed")
    
    def run(self):
        """Run the enhancer"""
        self.start()
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n🛑 Stopping...")
        finally:
            self.stop()

def main():
    """Main function"""
    enhancer = SimpleTaskbarEnhancer()
    enhancer.run()

if __name__ == "__main__":
    main()
