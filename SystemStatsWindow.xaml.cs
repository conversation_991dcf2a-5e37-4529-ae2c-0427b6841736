using System;
using System.Diagnostics;
using System.Management;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WindowsCustom
{
    public partial class SystemStatsWindow : Window
    {
        public SystemStatsWindow()
        {
            InitializeComponent();
            LoadStats();
        }

        private void LoadStats()
        {
            StatsPanel.Children.Clear();

            try
            {
                // CPU Usage
                AddStatItem("CPU Usage", GetCpuUsage());
                
                // Memory Usage
                AddStatItem("Memory Usage", GetMemoryUsage());
                
                // Disk Usage
                AddStatItem("Disk Usage", GetDiskUsage());
                
                // Running Processes
                AddStatItem("Running Processes", Process.GetProcesses().Length.ToString());
                
                // Uptime
                AddStatItem("System Uptime", GetSystemUptime());
                
                // OS Info
                AddStatItem("Operating System", Environment.OSVersion.ToString());
                
                // .NET Version
                AddStatItem(".NET Version", Environment.Version.ToString());
            }
            catch (Exception ex)
            {
                AddStatItem("Error", $"Failed to load stats: {ex.Message}");
            }
        }

        private void AddStatItem(string label, string value)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(60, 60, 60)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(100, 100, 100)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 5),
                Padding = new Thickness(15, 10)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var labelText = new TextBlock
            {
                Text = label,
                Foreground = new SolidColorBrush(Colors.LightGray),
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };

            var valueText = new TextBlock
            {
                Text = value,
                Foreground = new SolidColorBrush(Colors.White),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Center
            };

            Grid.SetColumn(labelText, 0);
            Grid.SetColumn(valueText, 1);

            grid.Children.Add(labelText);
            grid.Children.Add(valueText);
            border.Child = grid;

            StatsPanel.Children.Add(border);
        }

        private string GetCpuUsage()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("select * from Win32_PerfRawData_PerfOS_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        if (obj["Name"].ToString() == "_Total")
                        {
                            return "Available"; // Simplified for demo
                        }
                    }
                }
            }
            catch
            {
                // Fallback
            }
            return "N/A";
        }

        private string GetMemoryUsage()
        {
            try
            {
                var totalMemory = GC.GetTotalMemory(false);
                return $"{totalMemory / 1024 / 1024} MB";
            }
            catch
            {
                return "N/A";
            }
        }

        private string GetDiskUsage()
        {
            try
            {
                var drives = System.IO.DriveInfo.GetDrives();
                var cDrive = Array.Find(drives, d => d.Name == "C:\\");
                if (cDrive != null)
                {
                    var usedSpace = cDrive.TotalSize - cDrive.AvailableFreeSpace;
                    var usedPercent = (double)usedSpace / cDrive.TotalSize * 100;
                    return $"{usedPercent:F1}% ({usedSpace / 1024 / 1024 / 1024} GB used)";
                }
            }
            catch
            {
                // Ignore
            }
            return "N/A";
        }

        private string GetSystemUptime()
        {
            try
            {
                var uptime = TimeSpan.FromMilliseconds(Environment.TickCount);
                return $"{uptime.Days}d {uptime.Hours}h {uptime.Minutes}m";
            }
            catch
            {
                return "N/A";
            }
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadStats();
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
