<Window x:Class="WindowsCustom.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="WindowsCustom" Height="600" Width="1000"
        WindowStyle="None" 
        AllowsTransparency="True" 
        Background="Transparent"
        Topmost="True"
        ShowInTaskbar="False"
        WindowStartupLocation="Manual"
        Left="0" Top="{Binding TaskbarTop}">
    
    <Window.Resources>
        <!-- Hatsune Miku Theme -->
        <SolidColorBrush x:Key="MikuBlue" Color="#66BFF2"/>
        <SolidColorBrush x:Key="MikuTeal" Color="#39C5BB"/>
        <SolidColorBrush x:Key="MikuDark" Color="#1E3A8A"/>
        
        <!-- Red Theme -->
        <SolidColorBrush x:Key="RedLight" Color="#FF6B6B"/>
        <SolidColorBrush x:Key="RedDark" Color="#CC5555"/>
        
        <!-- Dark Theme -->
        <SolidColorBrush x:Key="DarkGray" Color="#1F1F1F"/>
        <SolidColorBrush x:Key="MediumGray" Color="#2D2D2D"/>
        
        <!-- Button Style -->
        <Style x:Key="TaskbarButton" TargetType="Button">
            <Setter Property="Background" Value="{DynamicResource ButtonBackground}"/>
            <Setter Property="Foreground" Value="{DynamicResource ButtonForeground}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorder}"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource ButtonHover}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{DynamicResource ButtonPressed}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <!-- Main Taskbar -->
    <Grid Height="{Binding TaskbarHeight}" Background="{DynamicResource TaskbarBackground}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/> <!-- Start Button Area -->
            <ColumnDefinition Width="Auto"/> <!-- Music Controls -->
            <ColumnDefinition Width="*"/>    <!-- App Buttons Area -->
            <ColumnDefinition Width="Auto"/> <!-- Settings Controls -->
            <ColumnDefinition Width="Auto"/> <!-- System Tray Area -->
        </Grid.ColumnDefinitions>
        
        <!-- Start Button Area (Spacer) -->
        <Border Grid.Column="0" Width="60" Background="Transparent"/>
        
        <!-- Music Controls -->
        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
            <Button Style="{StaticResource TaskbarButton}" Content="▶️" Click="PlayMusic" ToolTip="Play Music"/>
            <Button Style="{StaticResource TaskbarButton}" Content="⏸️" Click="PauseMusic" ToolTip="Pause Music"/>
            <Button Style="{StaticResource TaskbarButton}" Content="⏹️" Click="StopMusic" ToolTip="Stop Music"/>
            <Button Style="{StaticResource TaskbarButton}" Content="⏮️" Click="PreviousTrack" ToolTip="Previous Track"/>
            <Button Style="{StaticResource TaskbarButton}" Content="⏭️" Click="NextTrack" ToolTip="Next Track"/>
        </StackPanel>
        
        <!-- App Buttons Area -->
        <StackPanel Grid.Column="2" Name="AppButtonsPanel" Orientation="Horizontal" 
                   VerticalAlignment="Center" HorizontalAlignment="Left" Margin="20,0"/>
        
        <!-- Settings Controls -->
        <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
            <Button Style="{StaticResource TaskbarButton}" Content="⚙️" Click="OpenSettings" ToolTip="Settings"/>
            <Button Style="{StaticResource TaskbarButton}" Content="🎨" Click="ChangeTheme" ToolTip="Change Theme"/>
            <Button Style="{StaticResource TaskbarButton}" Content="📊" Click="ShowStats" ToolTip="System Stats"/>
            <Button Style="{StaticResource TaskbarButton}" Content="🔄" Click="RestoreTaskbar" ToolTip="Restore Windows Taskbar"/>
        </StackPanel>
        
        <!-- System Tray Area -->
        <StackPanel Grid.Column="4" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,10,0">
            <TextBlock Name="ClockDisplay" Foreground="{DynamicResource TextForeground}" 
                      FontFamily="Segoe UI" FontSize="14" VerticalAlignment="Center" Margin="10,0"/>
            <Border Width="100" Background="Transparent"/> <!-- System Tray Spacer -->
        </StackPanel>
    </Grid>
</Window>
