#!/usr/bin/env python3
"""
Complete WindowsCustom System Demo
Shows all features including themes, window management, and cleanup
"""

import json
import os
import time
import subprocess

def show_banner():
    """Show the WindowsCustom banner"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                    WindowsCustom System                     ║")
    print("║                   BY Aladdin Shenewa                        ║")
    print("║                                                              ║")
    print("║  🎨 Custom Themes  📱 Window Management  🔧 Settings GUI     ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()

def show_features():
    """Show all available features"""
    print("🚀 ENHANCED FEATURES:")
    print("=" * 50)
    
    print("✅ THEME SYSTEM:")
    print("   • Red Theme - Light red background, dark red buttons")
    print("   • Hatsune Miku Theme - Miku blue with teal text")
    print("   • Windows Dark Theme - Authentic Windows 11 styling")
    print()
    
    print("✅ WINDOW MANAGEMENT:")
    print("   • Minimized apps hidden from taskbar")
    print("   • Click window buttons to restore/minimize")
    print("   • Real-time window state updates")
    print("   • Proper Windows taskbar restoration on exit")
    print()
    
    print("✅ SETTINGS GUI:")
    print("   • Comprehensive theme selection")
    print("   • Feature enable/disable toggles")
    print("   • Advanced timer and font settings")
    print("   • Live color preview")
    print()
    
    print("✅ CLEANUP SYSTEM:")
    print("   • Windows taskbar automatically restored when app closes")
    print("   • Multiple cleanup triggers (close, Ctrl+C, system shutdown)")
    print("   • Graceful error handling")
    print()

def load_current_settings():
    """Load and display current settings"""
    if os.path.exists("settings.json"):
        with open("settings.json", "r") as f:
            settings = json.load(f)
        
        current_theme = settings.get("current_theme", "Hatsune Miku")
        themes = settings.get("themes", {})
        
        print("⚙️ CURRENT CONFIGURATION:")
        print("=" * 30)
        print(f"Active Theme: {current_theme}")
        
        if current_theme in themes:
            theme = themes[current_theme]
            print(f"Background:   {theme['background']}")
            print(f"Buttons:      {theme['buttons']}")
            print(f"Text:         {theme['text']}")
        
        print(f"Music Enabled: {settings.get('enable_music', True)}")
        print(f"Backup Enabled: {settings.get('enable_backup', True)}")
        print(f"Break Timer: {settings.get('break_timer_minutes', 59)} minutes")
        print()
        
        return settings
    else:
        print("❌ settings.json not found")
        return {}

def demo_menu():
    """Show demo menu"""
    print("🎯 DEMO OPTIONS:")
    print("=" * 20)
    print("1. Test Theme System")
    print("2. Open Settings GUI")
    print("3. Run Enhanced Taskbar")
    print("4. Test Window Management")
    print("5. Show All Tests")
    print("6. Exit")
    print()

def run_demo_option(choice, settings):
    """Run the selected demo option"""
    if choice == "1":
        print("🎨 Testing Theme System...")
        subprocess.run(["python", "test_themes.py"])
        
    elif choice == "2":
        print("⚙️ Opening Settings GUI...")
        print("(A new window should open)")
        subprocess.Popen(["python", "settings_gui.py"])
        time.sleep(2)
        
    elif choice == "3":
        print("🖥️ Starting Enhanced Taskbar...")
        print("(This will hide Windows taskbar and show custom one)")
        print("Press Ctrl+C in the taskbar window to exit")
        input("Press Enter to continue...")
        subprocess.run(["python", "demo_taskbar.py"])
        
    elif choice == "4":
        print("📱 Testing Window Management...")
        subprocess.run(["python", "test_window_management.py"])
        
    elif choice == "5":
        print("🧪 Running All Tests...")
        subprocess.run(["python", "test_system.py"])
        
    elif choice == "6":
        print("👋 Goodbye!")
        return False
        
    else:
        print("❌ Invalid choice")
    
    return True

def main():
    """Main demo function"""
    show_banner()
    show_features()
    
    settings = load_current_settings()
    
    while True:
        demo_menu()
        choice = input("Select option (1-6): ").strip()
        print()
        
        if not run_demo_option(choice, settings):
            break
        
        print()
        input("Press Enter to continue...")
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()
