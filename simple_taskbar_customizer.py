#!/usr/bin/env python3
"""
Simple and Effective Windows Taskbar Customizer
Changes the actual taskbar color and adds working controls
"""

import ctypes
from ctypes import wintypes
import win32gui
import win32con
import winreg
import tkinter as tk
import json
import os
import subprocess
import time

class SimpleTaskbarCustomizer:
    def __init__(self):
        self.taskbar_hwnd = None
        self.original_color = None
        self.controls_window = None
        
    def load_theme(self):
        """Load current theme colors"""
        try:
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            theme_name = settings.get("current_theme", "Hatsune Miku")
            themes = settings.get("themes", {})
            
            if theme_name in themes:
                return themes[theme_name]
        except:
            pass
        
        # Default <PERSON>sune Miku theme
        return {
            "background": "#66BFF2",
            "buttons": "#39C5BB",
            "text": "#1E3A8A"
        }
    
    def hex_to_rgb(self, hex_color):
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def rgb_to_colorref(self, r, g, b):
        """Convert RGB to Windows COLORREF"""
        return r | (g << 8) | (b << 16)
    
    def change_taskbar_color(self):
        """Change the actual Windows taskbar background color"""
        print("🎨 Changing taskbar color...")
        
        try:
            theme = self.load_theme()
            r, g, b = self.hex_to_rgb(theme["background"])
            
            # Set Windows accent color to our theme color
            reg_path = r"SOFTWARE\Microsoft\Windows\DWM"
            
            try:
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER, reg_path) as key:
                    # Set accent color
                    color_value = self.rgb_to_colorref(r, g, b)
                    winreg.SetValueEx(key, "AccentColor", 0, winreg.REG_DWORD, color_value)
                    winreg.SetValueEx(key, "ColorizationColor", 0, winreg.REG_DWORD, color_value)
                    
                    # Enable accent color on taskbar
                    winreg.SetValueEx(key, "ColorPrevalence", 0, winreg.REG_DWORD, 1)
                    
                print(f"✅ Taskbar color set to {theme['background']}")
                
                # Also set in personalization
                pers_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize"
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER, pers_path) as key:
                    # Enable accent color on taskbar
                    winreg.SetValueEx(key, "ColorPrevalence", 0, winreg.REG_DWORD, 1)
                    
                return True
                
            except Exception as e:
                print(f"Registry error: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Color change error: {e}")
            return False
    
    def find_taskbar(self):
        """Find Windows taskbar"""
        try:
            self.taskbar_hwnd = win32gui.FindWindow("Shell_TrayWnd", None)
            if self.taskbar_hwnd:
                print(f"✅ Found taskbar: {self.taskbar_hwnd}")
                return True
            else:
                print("❌ Taskbar not found")
                return False
        except Exception as e:
            print(f"❌ Taskbar search error: {e}")
            return False
    
    def create_integrated_controls(self):
        """Create controls that integrate with the taskbar"""
        print("🎵 Creating integrated controls...")
        
        try:
            # Get taskbar position
            if not self.taskbar_hwnd:
                return None
                
            rect = win32gui.GetWindowRect(self.taskbar_hwnd)
            taskbar_height = rect[3] - rect[1]
            taskbar_width = rect[2] - rect[0]
            
            theme = self.load_theme()
            
            # Create main controls window
            self.controls_window = tk.Tk()
            self.controls_window.title("WindowsCustom Controls")
            self.controls_window.overrideredirect(True)
            self.controls_window.configure(bg=theme["background"])
            self.controls_window.wm_attributes("-topmost", True)
            
            # Position controls in taskbar area
            controls_width = 200
            controls_height = taskbar_height - 4
            controls_x = rect[0] + 200  # After start button
            controls_y = rect[1] + 2
            
            self.controls_window.geometry(f"{controls_width}x{controls_height}+{controls_x}+{controls_y}")
            
            # Create main frame
            main_frame = tk.Frame(self.controls_window, bg=theme["background"])
            main_frame.pack(fill="both", expand=True, padx=2, pady=2)
            
            # Music controls section
            music_frame = tk.Frame(main_frame, bg=theme["buttons"], relief="raised", bd=1)
            music_frame.pack(side="left", fill="y", padx=2)
            
            tk.Button(music_frame, text="▶️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=0,
                     command=self.play_music).pack(side="left", padx=1, pady=1)
            
            tk.Button(music_frame, text="⏸️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=0,
                     command=self.pause_music).pack(side="left", padx=1, pady=1)
            
            tk.Button(music_frame, text="⏹️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=0,
                     command=self.stop_music).pack(side="left", padx=1, pady=1)
            
            # Settings controls section
            settings_frame = tk.Frame(main_frame, bg=theme["buttons"], relief="raised", bd=1)
            settings_frame.pack(side="right", fill="y", padx=2)
            
            tk.Button(settings_frame, text="⚙️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=0,
                     command=self.open_settings).pack(side="left", padx=1, pady=1)
            
            tk.Button(settings_frame, text="🎨", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=0,
                     command=self.quick_theme).pack(side="left", padx=1, pady=1)
            
            tk.Button(settings_frame, text="🔄", font=("Segoe UI", 8), width=3,
                     bg="#FF4444", fg="white", border=0,
                     command=self.revert_changes).pack(side="left", padx=1, pady=1)
            
            print("✅ Integrated controls created")
            return True
            
        except Exception as e:
            print(f"❌ Controls creation error: {e}")
            return False
    
    def play_music(self):
        """Play music"""
        try:
            import music_controls
            music_controls.play_music()
            print("🎵 Music started")
        except Exception as e:
            print(f"Music error: {e}")
    
    def pause_music(self):
        """Pause music"""
        try:
            import music_controls
            music_controls.pause_music()
            print("⏸️ Music paused")
        except Exception as e:
            print(f"Music error: {e}")
    
    def stop_music(self):
        """Stop music"""
        try:
            import music_controls
            music_controls.stop_music()
            print("⏹️ Music stopped")
        except Exception as e:
            print(f"Music error: {e}")
    
    def open_settings(self):
        """Open settings"""
        try:
            subprocess.Popen(["python", "settings_gui.py"])
            print("⚙️ Settings opened")
        except Exception as e:
            print(f"Settings error: {e}")
    
    def quick_theme(self):
        """Quick theme selector"""
        popup = tk.Toplevel(self.controls_window)
        popup.title("Quick Theme")
        popup.geometry("200x120")
        popup.configure(bg="#2D2D2D")
        popup.wm_attributes("-topmost", True)
        
        tk.Label(popup, text="Select Theme", bg="#2D2D2D", fg="white").pack(pady=5)
        
        tk.Button(popup, text="🔴 Red", bg="#FF6B6B", fg="white",
                 command=lambda: self.apply_theme("Red", popup)).pack(pady=1)
        
        tk.Button(popup, text="💙 Miku", bg="#66BFF2", fg="#1E3A8A",
                 command=lambda: self.apply_theme("Hatsune Miku", popup)).pack(pady=1)
        
        tk.Button(popup, text="⚫ Dark", bg="#1F1F1F", fg="white",
                 command=lambda: self.apply_theme("Windows Dark", popup)).pack(pady=1)
    
    def apply_theme(self, theme_name, popup):
        """Apply theme and restart"""
        try:
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            settings["current_theme"] = theme_name
            
            with open("settings.json", "w") as f:
                json.dump(settings, f, indent=2)
            
            popup.destroy()
            print(f"🎨 Applied {theme_name} theme")
            
            # Restart to apply new theme
            self.restart()
            
        except Exception as e:
            print(f"Theme error: {e}")
    
    def revert_changes(self):
        """Revert all changes"""
        print("🔄 Reverting changes...")
        
        try:
            # Reset accent color
            reg_path = r"SOFTWARE\Microsoft\Windows\DWM"
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, reg_path) as key:
                winreg.SetValueEx(key, "ColorPrevalence", 0, winreg.REG_DWORD, 0)
            
            pers_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize"
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, pers_path) as key:
                winreg.SetValueEx(key, "ColorPrevalence", 0, winreg.REG_DWORD, 0)
            
            print("✅ Changes reverted")
            
            # Close controls
            if self.controls_window:
                self.controls_window.destroy()
            
            # Restart Explorer
            subprocess.run(["taskkill", "/f", "/im", "explorer.exe"], capture_output=True)
            time.sleep(1)
            subprocess.Popen("explorer.exe")
            
        except Exception as e:
            print(f"Revert error: {e}")
    
    def restart(self):
        """Restart the customizer"""
        if self.controls_window:
            self.controls_window.destroy()
        
        time.sleep(1)
        self.run()
    
    def run(self):
        """Run the taskbar customizer"""
        print("Simple Windows Taskbar Customizer")
        print("BY Aladdin Shenewa")
        print("=" * 40)
        
        # Find taskbar
        if not self.find_taskbar():
            print("❌ Cannot customize - taskbar not found")
            return False
        
        # Change taskbar color
        if self.change_taskbar_color():
            print("✅ Taskbar color changed")
        
        # Create controls
        if self.create_integrated_controls():
            print("✅ Controls integrated")
            
            print("\n🎉 TASKBAR CUSTOMIZED!")
            print("🎨 Full taskbar color changed")
            print("🎵 Music controls: ▶️⏸️⏹️")
            print("⚙️ Settings: ⚙️🎨🔄")
            print("\nPress Ctrl+C to stop")
            
            try:
                self.controls_window.mainloop()
            except KeyboardInterrupt:
                print("\n🛑 Stopping...")
                self.revert_changes()
        else:
            print("❌ Controls creation failed")
            return False

def main():
    """Main function"""
    customizer = SimpleTaskbarCustomizer()
    customizer.run()

if __name__ == "__main__":
    main()
