using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using Newtonsoft.Json;
using NAudio.Wave;
using System.Linq;

namespace WindowsCustom
{
    public partial class MainWindow : Window
    {
        private DispatcherTimer clockTimer;
        private DispatcherTimer appUpdateTimer;
        private MusicPlayer musicPlayer;
        private ThemeManager themeManager;
        private TaskbarManager taskbarManager;
        private List<AppButton> appButtons = new List<AppButton>();

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        public MainWindow()
        {
            InitializeComponent();
            InitializeComponents();
            SetupTaskbar();
            StartTimers();
        }

        private void InitializeComponents()
        {
            musicPlayer = new MusicPlayer();
            themeManager = new ThemeManager();
            taskbarManager = new TaskbarManager();
            
            // Apply initial theme
            themeManager.ApplyTheme(this, "HatsuneMiku");
        }

        private void SetupTaskbar()
        {
            // Hide Windows taskbar
            taskbarManager.HideWindowsTaskbar();
            
            // Position our taskbar
            var screenHeight = SystemParameters.PrimaryScreenHeight;
            var taskbarHeight = 48;
            
            this.Height = taskbarHeight;
            this.Width = SystemParameters.PrimaryScreenWidth;
            this.Top = screenHeight - taskbarHeight;
            this.Left = 0;
        }

        private void StartTimers()
        {
            // Clock timer
            clockTimer = new DispatcherTimer();
            clockTimer.Interval = TimeSpan.FromSeconds(1);
            clockTimer.Tick += UpdateClock;
            clockTimer.Start();

            // App update timer
            appUpdateTimer = new DispatcherTimer();
            appUpdateTimer.Interval = TimeSpan.FromSeconds(2);
            appUpdateTimer.Tick += UpdateAppButtons;
            appUpdateTimer.Start();

            UpdateClock(null, null);
            UpdateAppButtons(null, null);
        }

        private void UpdateClock(object sender, EventArgs e)
        {
            ClockDisplay.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void UpdateAppButtons(object sender, EventArgs e)
        {
            try
            {
                var runningApps = taskbarManager.GetRunningApplications();

                // Clear existing buttons
                AppButtonsPanel.Children.Clear();
                appButtons.Clear();

                foreach (var app in runningApps.Take(8)) // Limit to 8 apps
                {
                    var button = new Button
                    {
                        Content = app.Title.Length > 15 ? app.Title.Substring(0, 15) + "..." : app.Title,
                        Style = (Style)FindResource("TaskbarButton"),
                        Tag = app.Handle,
                        Width = 120,
                        ToolTip = app.Title
                    };

                    var appHandle = app.Handle; // Capture for closure
                    button.Click += (s, e) => taskbarManager.SwitchToApplication(appHandle);

                    AppButtonsPanel.Children.Add(button);
                    appButtons.Add(new AppButton { Button = button, App = app });
                }
            }
            catch (Exception ex)
            {
                // Log error but don't crash
                Debug.WriteLine($"Error updating app buttons: {ex.Message}");
            }
        }

        // Music Controls
        private void PlayMusic(object sender, RoutedEventArgs e)
        {
            musicPlayer.Play();
        }

        private void PauseMusic(object sender, RoutedEventArgs e)
        {
            musicPlayer.Pause();
        }

        private void StopMusic(object sender, RoutedEventArgs e)
        {
            musicPlayer.Stop();
        }

        private void PreviousTrack(object sender, RoutedEventArgs e)
        {
            musicPlayer.Previous();
        }

        private void NextTrack(object sender, RoutedEventArgs e)
        {
            musicPlayer.Next();
        }

        // Settings Controls
        private void OpenSettings(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.Show();
        }

        private void ChangeTheme(object sender, RoutedEventArgs e)
        {
            var themeWindow = new ThemeSelector();
            themeWindow.ThemeChanged += (theme) => themeManager.ApplyTheme(this, theme);
            themeWindow.Show();
        }

        private void ShowStats(object sender, RoutedEventArgs e)
        {
            var statsWindow = new SystemStatsWindow();
            statsWindow.Show();
        }

        private void RestoreTaskbar(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "This will restore the Windows taskbar and close WindowsCustom. Continue?",
                "Restore Windows Taskbar",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                taskbarManager.ShowWindowsTaskbar();
                Application.Current.Shutdown();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            clockTimer?.Stop();
            appUpdateTimer?.Stop();
            musicPlayer?.Dispose();
            taskbarManager?.ShowWindowsTaskbar();
            base.OnClosed(e);
        }
    }

    public class AppButton
    {
        public Button Button { get; set; }
        public RunningApp App { get; set; }
    }

    public class RunningApp
    {
        public IntPtr Handle { get; set; }
        public string Title { get; set; }
        public string ProcessName { get; set; }
    }
}
