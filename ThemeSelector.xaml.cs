using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace WindowsCustom
{
    public partial class ThemeSelector : Window
    {
        public event Action<string> ThemeChanged;
        private ThemeManager themeManager;

        public ThemeSelector()
        {
            InitializeComponent();
            themeManager = new ThemeManager();
            LoadThemes();
        }

        private void LoadThemes()
        {
            var themes = themeManager.GetAvailableThemes();
            
            foreach (var themeName in themes)
            {
                var theme = themeManager.GetTheme(themeName);
                var themeButton = CreateThemeButton(themeName, theme);
                ThemeGrid.Children.Add(themeButton);
            }
        }

        private Border CreateThemeButton(string themeName, Theme theme)
        {
            var border = new Border
            {
                Width = 200,
                Height = 120,
                Margin = new Thickness(10),
                BorderBrush = new SolidColorBrush(Colors.Gray),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Background = theme.TaskbarBackground,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            var stackPanel = new StackPanel
            {
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var titleText = new TextBlock
            {
                Text = theme.Name,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = theme.TextForeground,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var buttonPreview = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // Create mini preview buttons
            for (int i = 0; i < 3; i++)
            {
                var miniButton = new Border
                {
                    Width = 30,
                    Height = 20,
                    Margin = new Thickness(2),
                    Background = theme.ButtonBackground,
                    BorderBrush = theme.ButtonBorder,
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(2)
                };

                var miniText = new TextBlock
                {
                    Text = "●",
                    Foreground = theme.ButtonForeground,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontSize = 8
                };

                miniButton.Child = miniText;
                buttonPreview.Children.Add(miniButton);
            }

            stackPanel.Children.Add(titleText);
            stackPanel.Children.Add(buttonPreview);
            border.Child = stackPanel;

            // Add click handler
            border.MouseLeftButtonUp += (s, e) =>
            {
                ThemeChanged?.Invoke(themeName);
                this.Close();
            };

            // Add hover effect
            border.MouseEnter += (s, e) =>
            {
                border.BorderBrush = new SolidColorBrush(Colors.White);
                border.BorderThickness = new Thickness(3);
            };

            border.MouseLeave += (s, e) =>
            {
                border.BorderBrush = new SolidColorBrush(Colors.Gray);
                border.BorderThickness = new Thickness(2);
            };

            return border;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
