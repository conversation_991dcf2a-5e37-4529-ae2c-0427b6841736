#!/usr/bin/env python3
"""
Permanent Windows Taskbar Modifier
Actually modifies the Windows taskbar elements and keeps changes persistent
"""

import ctypes
from ctypes import wintypes
import win32gui
import win32con
import win32api
import winreg
import tkinter as tk
import json
import os
import subprocess
import threading
import time

class PermanentTaskbarModifier:
    def __init__(self):
        self.taskbar_hwnd = None
        self.start_button_hwnd = None
        self.search_box_hwnd = None
        self.system_tray_hwnd = None
        self.music_controls_hwnd = None
        self.settings_controls_hwnd = None
        self.running = False
        
    def find_taskbar_elements(self):
        """Find all Windows taskbar elements"""
        print("🔍 Finding Windows taskbar elements...")
        
        try:
            # Find main taskbar
            self.taskbar_hwnd = win32gui.FindWindow("Shell_TrayWnd", None)
            if not self.taskbar_hwnd:
                print("❌ Could not find main taskbar")
                return False
            
            # Find start button
            self.start_button_hwnd = win32gui.FindWindowEx(self.taskbar_hwnd, 0, "Start", None)
            if not self.start_button_hwnd:
                # Try alternative class name
                self.start_button_hwnd = win32gui.FindWindowEx(self.taskbar_hwnd, 0, "Button", None)
            
            # Find search box
            self.search_box_hwnd = win32gui.FindWindowEx(self.taskbar_hwnd, 0, "TraySearchBoxWnd", None)
            
            # Find system tray
            self.system_tray_hwnd = win32gui.FindWindowEx(self.taskbar_hwnd, 0, "TrayNotifyWnd", None)
            
            print(f"✅ Taskbar: {self.taskbar_hwnd}")
            print(f"✅ Start Button: {self.start_button_hwnd}")
            print(f"✅ Search Box: {self.search_box_hwnd}")
            print(f"✅ System Tray: {self.system_tray_hwnd}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error finding taskbar elements: {e}")
            return False
    
    def modify_taskbar_registry(self):
        """Modify registry to customize taskbar permanently"""
        print("🔧 Modifying taskbar registry settings...")
        
        try:
            # Registry path for taskbar settings
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                # Hide search box (we'll add our own)
                winreg.SetValueEx(key, "SearchboxTaskbarMode", 0, winreg.REG_DWORD, 0)
                
                # Show small taskbar buttons for more space
                winreg.SetValueEx(key, "TaskbarSmallIcons", 0, winreg.REG_DWORD, 1)
                
                # Hide task view button
                winreg.SetValueEx(key, "ShowTaskViewButton", 0, winreg.REG_DWORD, 0)
                
                # Hide widgets button
                winreg.SetValueEx(key, "TaskbarDa", 0, winreg.REG_DWORD, 0)
                
                # Hide chat button
                winreg.SetValueEx(key, "TaskbarMn", 0, winreg.REG_DWORD, 0)
                
            print("✅ Registry modifications applied")
            return True
            
        except Exception as e:
            print(f"❌ Registry modification error: {e}")
            return False
    
    def apply_theme_to_taskbar(self):
        """Apply custom theme colors to Windows taskbar"""
        print("🎨 Applying custom theme to taskbar...")
        
        try:
            # Load theme colors
            theme = self.load_theme_colors()
            
            # Registry path for personalization
            reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize"
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                # Enable dark mode for taskbar
                winreg.SetValueEx(key, "SystemUsesLightTheme", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "AppsUseLightTheme", 0, winreg.REG_DWORD, 0)
            
            # Apply accent color
            accent_reg_path = r"SOFTWARE\Microsoft\Windows\DWM"
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, accent_reg_path, 0, winreg.KEY_SET_VALUE) as key:
                    # Convert theme color to Windows accent color format
                    color_hex = theme["background"].replace("#", "")
                    color_rgb = tuple(int(color_hex[i:i+2], 16) for i in (0, 2, 4))
                    color_dword = color_rgb[2] << 16 | color_rgb[1] << 8 | color_rgb[0]
                    
                    winreg.SetValueEx(key, "AccentColor", 0, winreg.REG_DWORD, color_dword)
                    winreg.SetValueEx(key, "ColorizationColor", 0, winreg.REG_DWORD, color_dword)
            except:
                pass
            
            print(f"✅ Applied {theme} theme to taskbar")
            return True
            
        except Exception as e:
            print(f"❌ Theme application error: {e}")
            return False
    
    def load_theme_colors(self):
        """Load current theme colors"""
        try:
            with open("settings.json", "r") as f:
                settings = json.load(f)
            
            current_theme = settings.get("current_theme", "Hatsune Miku")
            themes = settings.get("themes", {})
            
            if current_theme in themes:
                return themes[current_theme]
        except:
            pass
        
        # Default Hatsune Miku theme
        return {
            "background": "#66BFF2",
            "buttons": "#39C5BB",
            "text": "#1E3A8A"
        }
    
    def create_persistent_controls(self):
        """Create persistent controls that stay with taskbar"""
        print("🎵 Creating persistent taskbar controls...")

        try:
            # Create a hidden root window
            root = tk.Tk()
            root.withdraw()

            # Get taskbar position
            taskbar_rect = win32gui.GetWindowRect(self.taskbar_hwnd)
            theme = self.load_theme_colors()

            # Create music controls window
            music_window = tk.Toplevel(root)
            music_window.overrideredirect(True)
            music_window.configure(bg=theme["background"])
            music_window.wm_attributes("-topmost", True)

            # Position and size
            width = 120
            height = taskbar_rect[3] - taskbar_rect[1] - 4
            x = taskbar_rect[0] + 180  # After start button
            y = taskbar_rect[1] + 2

            music_window.geometry(f"{width}x{height}+{x}+{y}")

            # Create controls frame
            frame = tk.Frame(music_window, bg=theme["background"])
            frame.pack(fill="both", expand=True, padx=2, pady=2)

            # Music control buttons
            tk.Button(frame, text="▶️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=1,
                     command=self.play_music).pack(side="left", padx=1)

            tk.Button(frame, text="⏸️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=1,
                     command=self.pause_music).pack(side="left", padx=1)

            tk.Button(frame, text="⏹️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=1,
                     command=self.stop_music).pack(side="left", padx=1)

            # Settings controls window
            settings_window = tk.Toplevel(root)
            settings_window.overrideredirect(True)
            settings_window.configure(bg=theme["background"])
            settings_window.wm_attributes("-topmost", True)

            # Position before system tray
            settings_width = 80
            settings_x = taskbar_rect[2] - settings_width - 120
            settings_window.geometry(f"{settings_width}x{height}+{settings_x}+{y}")

            # Settings frame
            settings_frame = tk.Frame(settings_window, bg=theme["background"])
            settings_frame.pack(fill="both", expand=True, padx=2, pady=2)

            tk.Button(settings_frame, text="⚙️", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=1,
                     command=self.open_settings).pack(side="left", padx=1)

            tk.Button(settings_frame, text="🎨", font=("Segoe UI", 8), width=3,
                     bg=theme["buttons"], fg=theme["text"], border=1,
                     command=self.quick_theme).pack(side="left", padx=1)

            print("✅ Persistent controls created")

            # Keep windows alive
            self.running = True

            def keep_alive():
                while self.running:
                    try:
                        root.update()
                        time.sleep(0.1)
                    except:
                        break

            # Start keep-alive thread
            threading.Thread(target=keep_alive, daemon=True).start()

            return root, music_window, settings_window

        except Exception as e:
            print(f"❌ Persistent controls error: {e}")
            return None, None, None
    

    
    def restart_explorer(self):
        """Restart Windows Explorer to apply changes"""
        print("🔄 Restarting Windows Explorer to apply changes...")
        
        try:
            # Kill explorer
            subprocess.run(["taskkill", "/f", "/im", "explorer.exe"], capture_output=True)
            time.sleep(2)
            
            # Start explorer
            subprocess.Popen("explorer.exe")
            time.sleep(3)
            
            print("✅ Windows Explorer restarted")
            return True
            
        except Exception as e:
            print(f"❌ Explorer restart error: {e}")
            return False
    
    def make_changes_persistent(self):
        """Make taskbar changes persistent across reboots"""
        print("💾 Making changes persistent...")
        
        try:
            # Create startup entry for WindowsCustom
            startup_path = os.path.join(os.getenv('APPDATA'), 
                                      'Microsoft', 'Windows', 'Start Menu', 
                                      'Programs', 'Startup')
            
            # Create batch file to start WindowsCustom
            batch_content = f'''@echo off
cd /d "{os.getcwd()}"
python permanent_taskbar_modifier.py --startup
'''
            
            batch_file = os.path.join(startup_path, "WindowsCustom.bat")
            with open(batch_file, 'w') as f:
                f.write(batch_content)
            
            print("✅ Startup entry created")
            
            # Set registry flag for persistence
            reg_path = r"SOFTWARE\WindowsCustom"
            try:
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER, reg_path) as key:
                    winreg.SetValueEx(key, "PersistentMode", 0, winreg.REG_DWORD, 1)
                    winreg.SetValueEx(key, "ThemeApplied", 0, winreg.REG_SZ, 
                                    json.dumps(self.load_theme_colors()))
            except:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ Persistence setup error: {e}")
            return False
    
    def play_music(self):
        """Play music"""
        try:
            import music_controls
            music_controls.play_music()
        except:
            pass
    
    def pause_music(self):
        """Pause music"""
        try:
            import music_controls
            music_controls.pause_music()
        except:
            pass
    
    def stop_music(self):
        """Stop music"""
        try:
            import music_controls
            music_controls.stop_music()
        except:
            pass
    
    def open_settings(self):
        """Open settings"""
        try:
            subprocess.Popen(["python", "settings_gui.py"])
        except:
            pass
    
    def quick_theme(self):
        """Quick theme switcher"""
        # Implementation for quick theme switching
        pass
    
    def apply_permanent_modifications(self):
        """Apply all permanent modifications"""
        print("🚀 Applying permanent taskbar modifications...")
        print("=" * 50)

        success_count = 0

        # Step 1: Find taskbar elements
        if self.find_taskbar_elements():
            success_count += 1
            print("✅ Step 1: Taskbar elements found")

        # Step 2: Modify registry for permanent changes
        if self.modify_taskbar_registry():
            success_count += 1
            print("✅ Step 2: Registry modifications applied")

        # Step 3: Apply custom theme
        if self.apply_theme_to_taskbar():
            success_count += 1
            print("✅ Step 3: Custom theme applied")

        # Step 4: Make changes persistent
        if self.make_changes_persistent():
            success_count += 1
            print("✅ Step 4: Persistence configured")

        # Step 5: Restart Explorer to apply changes
        print("🔄 Step 5: Restarting Explorer...")
        if self.restart_explorer():
            success_count += 1
            print("✅ Step 5: Explorer restarted")

            # Wait for Explorer to fully restart
            time.sleep(5)

            # Re-find taskbar after restart
            if self.find_taskbar_elements():
                print("✅ Taskbar re-detected after restart")

                # Step 6: Create persistent controls
                print("🎵 Step 6: Creating persistent controls...")
                root, music_window, settings_window = self.create_persistent_controls()

                if root and music_window and settings_window:
                    success_count += 1
                    print("✅ Step 6: Persistent controls created")

                    # Keep controls running
                    print("\n🎉 TASKBAR PERMANENTLY MODIFIED!")
                    print("🎵 Music controls: ▶️⏸️⏹️")
                    print("⚙️ Settings controls: ⚙️🎨")
                    print("🎨 Custom theme applied")
                    print("💾 Changes persist across reboots")
                    print("\nPress Ctrl+C to stop (controls will remain)")

                    try:
                        root.mainloop()
                    except KeyboardInterrupt:
                        print("\n🛑 Stopping control interface...")
                        self.running = False
                        root.destroy()

                    return True

        print(f"\n📊 Applied {success_count}/6 modifications successfully")

        if success_count >= 4:
            print("✅ Basic modifications applied!")
            print("⚠️  Some advanced features may need manual setup")
            return True
        else:
            print("❌ Critical modifications failed")
            return False

def main():
    """Main function"""
    import sys
    
    print("WindowsCustom Permanent Taskbar Modifier")
    print("BY Aladdin Shenewa")
    print("=" * 50)
    
    # Check if running from startup
    startup_mode = "--startup" in sys.argv
    
    if startup_mode:
        print("🔄 Running in startup mode...")
    else:
        print("🎯 Permanently modifying Windows taskbar...")
        print("⚠️  This will restart Windows Explorer")
        
        choice = input("\nContinue? (y/n): ").lower().strip()
        if choice != 'y':
            print("Operation cancelled")
            return
    
    modifier = PermanentTaskbarModifier()
    
    if modifier.apply_permanent_modifications():
        if not startup_mode:
            print("\n🎉 Taskbar permanently modified!")
            print("Your customizations will persist across reboots.")
    else:
        print("\n❌ Modification failed")

if __name__ == "__main__":
    main()
