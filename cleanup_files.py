#!/usr/bin/env python3
"""
Clean up unnecessary files that could cause issues
"""

import os
import glob

def cleanup_unnecessary_files():
    """Remove unnecessary files"""
    print("🧹 Cleaning up unnecessary files...")
    
    # Files to remove
    files_to_remove = [
        "launch_windowscustom.py",  # Has error text at top
        "*.md",  # Documentation files
        "test_*.py",  # Old test files (except our new one)
        "demo_*.py",  # Demo files
        "color_reference.py",
        "taskbar_editor.py",
        "simple_taskbar_enhancer.py",
        "start_menu.py",  # Old start menu
    ]
    
    # Keep these files
    keep_files = [
        "test_permanent_modifier.py",  # Our new test
        "permanent_taskbar_modifier.py",  # Main modifier
        "cleanup_files.py",  # This file
        "main.py",
        "settings.py", 
        "settings_gui.py",
        "taskbar.py",
        "music_player.py",
        "music_controls.py",
        "backup.py",
        "utils.py",
        "settings.json",
        "edge_blocker.py",
        "splash_screen.py"
    ]
    
    removed_count = 0
    
    for pattern in files_to_remove:
        for file_path in glob.glob(pattern):
            if os.path.basename(file_path) not in keep_files:
                try:
                    os.remove(file_path)
                    print(f"🗑️  Removed: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"❌ Could not remove {file_path}: {e}")
    
    print(f"\n✅ Cleaned up {removed_count} unnecessary files")
    
    # Show remaining files
    print("\n📁 Remaining core files:")
    for file in sorted(os.listdir(".")):
        if file.endswith(".py") or file.endswith(".json"):
            print(f"   {file}")

def fix_launch_file():
    """Fix the launch file that has error text"""
    print("\n🔧 Fixing corrupted launch file...")
    
    try:
        # Read the file
        with open("launch_windowscustom.py", "r") as f:
            content = f.read()
        
        # Remove error text from beginning
        if content.startswith("Error updating"):
            lines = content.split('\n')
            # Find the first proper Python line
            start_index = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('#!/usr/bin/env python3'):
                    start_index = i
                    break
            
            if start_index > 0:
                # Rewrite file without error text
                clean_content = '\n'.join(lines[start_index:])
                with open("launch_windowscustom.py", "w") as f:
                    f.write(clean_content)
                print("✅ Fixed launch file")
            else:
                print("⚠️  Could not find clean start in launch file")
        else:
            print("✅ Launch file is already clean")
            
    except Exception as e:
        print(f"❌ Could not fix launch file: {e}")

def main():
    """Main cleanup function"""
    print("WindowsCustom File Cleanup")
    print("BY Aladdin Shenewa")
    print("=" * 30)
    
    print("🎯 CLEANING UP:")
    print("• Removing test files that could cause conflicts")
    print("• Removing documentation files")
    print("• Removing demo files")
    print("• Fixing corrupted files")
    print("• Keeping only essential files")
    
    choice = input("\nProceed with cleanup? (y/n): ").lower().strip()
    
    if choice == 'y':
        fix_launch_file()
        cleanup_unnecessary_files()
        
        print("\n🎉 CLEANUP COMPLETE!")
        print("\n📋 NEXT STEPS:")
        print("1. Run: python test_permanent_modifier.py")
        print("2. If tests pass, run: python permanent_taskbar_modifier.py")
        print("3. Your taskbar will be permanently customized!")
        
    else:
        print("Cleanup cancelled")

if __name__ == "__main__":
    main()
