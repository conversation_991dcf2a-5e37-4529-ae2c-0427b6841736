{"version": 3, "targets": {"net9.0-windows7.0": {"Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Windows.SDK.Contracts/10.0.22621.2428": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "compile": {"ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd": {}, "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd": {}, "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd": {}, "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd": {}, "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.SharedPackageContainerContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Update.WindowsUpdateContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD": {}, "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd": {}, "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD": {}, "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd": {}, "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd": {}, "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd": {}, "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd": {}, "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd": {}, "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.WindowTabManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.UIAutomation.UIAutomationContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd": {}, "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd": {}, "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd": {}, "ref/netstandard2.0/Windows.WinMD": {}}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "NAudio/2.2.1": {"type": "package", "dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinForms": "2.2.1", "NAudio.WinMM": "2.2.1"}, "compile": {"lib/net6.0-windows7.0/NAudio.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/NAudio.dll": {"related": ".xml"}}}, "NAudio.Asio/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}}, "NAudio.Core/2.2.1": {"type": "package", "compile": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}}, "NAudio.Midi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}}, "NAudio.Wasapi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}}, "NAudio.WinForms/2.2.1": {"type": "package", "dependencies": {"NAudio.WinMM": "2.2.1"}, "compile": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "NAudio.WinMM/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/7.0.2": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.InteropServices.WindowsRuntime.dll": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.0.0"}, "compile": {"ref/netstandard2.0/System.Runtime.WindowsRuntime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.WindowsRuntime.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.0.0", "System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Windows.SDK.Contracts/10.0.22621.2428": {"sha512": "W+mt71YdJcDtNzgVrRSHQYdj2/p0t7Dlvd0MRVwT57S54uxzsw3Qyv6RjGTUZ6nwOLYe/MA4IMeCHQZqPi3Tpw==", "type": "package", "path": "microsoft.windows.sdk.contracts/10.0.22621.2428", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Windows.SDK.Contracts.props", "build/Microsoft.Windows.SDK.Contracts.targets", "c/Catalogs/cat353be8f91891a6a5761b9ac157fa2ff1.cat", "c/Catalogs/cat4ec14c5368b7642563c070cd168960a8.cat", "c/Catalogs/cate59830bab4961666e8d8c2af1e5fa771.cat", "c/Catalogs/catf105a73f98cfc88c7b64d8f7b39a474c.cat", "microsoft.windows.sdk.contracts.10.0.22621.2428.nupkg.sha512", "microsoft.windows.sdk.contracts.nuspec", "ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd", "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd", "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd", "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd", "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd", "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd", "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd", "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd", "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd", "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd", "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd", "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd", "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd", "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.SharedPackageContainerContract.winmd", "ref/netstandard2.0/Windows.Management.Update.WindowsUpdateContract.winmd", "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd", "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd", "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd", "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd", "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd", "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd", "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd", "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD", "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd", "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd", "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd", "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd", "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD", "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd", "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd", "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd", "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd", "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd", "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd", "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd", "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd", "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd", "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd", "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd", "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd", "ref/netstandard2.0/Windows.UI.Shell.WindowTabManagerContract.winmd", "ref/netstandard2.0/Windows.UI.UIAutomation.UIAutomationContract.winmd", "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd", "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd", "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd", "ref/netstandard2.0/Windows.WinMD", "ref/netstandard2.0/en/Windows.AI.MachineLearning.MachineLearningContract.xml", "ref/netstandard2.0/en/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsPhoneContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsVoipContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.LockScreenCallContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.FullTrustAppContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.Core.SearchCoreContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.SearchContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.SocialInfo.SocialInfoContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.StartupTaskContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Wallet.WalletContract.xml", "ref/netstandard2.0/en/Windows.Devices.Custom.CustomDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.DevicesLowLevelContract.xml", "ref/netstandard2.0/en/Windows.Devices.Portable.PortableDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.Extensions.ExtensionsContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.PrintersContract.xml", "ref/netstandard2.0/en/Windows.Devices.Scanners.ScannerDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardEmulatorContract.xml", "ref/netstandard2.0/en/Windows.Devices.Sms.LegacySmsApiContract.xml", "ref/netstandard2.0/en/Windows.Foundation.FoundationContract.xml", "ref/netstandard2.0/en/Windows.Foundation.UniversalApiContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Input.GamingInputPreviewContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Preview.GamesEnumerationContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GameChatOverlayContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GamingUIProviderContract.xml", "ref/netstandard2.0/en/Windows.Gaming.XboxLive.StorageApiContract.xml", "ref/netstandard2.0/en/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.xml", "ref/netstandard2.0/en/Windows.Graphics.Printing3D.Printing3DContract.xml", "ref/netstandard2.0/en/Windows.Management.Deployment.Preview.DeploymentPreviewContract.xml", "ref/netstandard2.0/en/Windows.Management.Workplace.WorkplaceSettingsContract.xml", "ref/netstandard2.0/en/Windows.Media.AppBroadcasting.AppBroadcastingContract.xml", "ref/netstandard2.0/en/Windows.Media.AppRecording.AppRecordingContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppBroadcastContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureMetadataContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.CameraCaptureUIContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.GameBarContract.xml", "ref/netstandard2.0/en/Windows.Media.Devices.CallControlContract.xml", "ref/netstandard2.0/en/Windows.Media.MediaControlContract.xml", "ref/netstandard2.0/en/Windows.Media.Playlists.PlaylistsContract.xml", "ref/netstandard2.0/en/Windows.Media.Protection.ProtectionRenewalContract.xml", "ref/netstandard2.0/en/Windows.Networking.Connectivity.WwanContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.xml", "ref/netstandard2.0/en/Windows.Networking.Sockets.ControlChannelTriggerContract.xml", "ref/netstandard2.0/en/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.xml", "ref/netstandard2.0/en/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.xml", "ref/netstandard2.0/en/Windows.Phone.PhoneContract.xml", "ref/netstandard2.0/en/Windows.Phone.StartScreen.DualSimTileContract.xml", "ref/netstandard2.0/en/Windows.Security.EnterpriseData.EnterpriseDataContract.xml", "ref/netstandard2.0/en/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.xml", "ref/netstandard2.0/en/Windows.Security.Isolation.IsolatedWindowsEnvironmentContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.GuidanceContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.LocalSearchContract.xml", "ref/netstandard2.0/en/Windows.Services.Store.StoreContract.xml", "ref/netstandard2.0/en/Windows.Services.TargetedContent.TargetedContentContract.xml", "ref/netstandard2.0/en/Windows.Storage.Provider.CloudFilesContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileHardwareTokenContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileRetailInfoContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileSharedModeContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.xml", "ref/netstandard2.0/en/Windows.System.SystemManagementContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileLockScreenContract.xml", "ref/netstandard2.0/en/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.CoreWindowDialogsContract.xml", "ref/netstandard2.0/en/Windows.UI.Shell.SecurityAppManagerContract.xml", "ref/netstandard2.0/en/Windows.UI.UIAutomation.UIAutomationContract.xml", "ref/netstandard2.0/en/Windows.UI.ViewManagement.ViewManagementViewScalingContract.xml", "ref/netstandard2.0/en/Windows.UI.WebUI.Core.WebUICommandBarContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Core.Direct.XamlDirectContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Hosting.HostingContract.xml", "ref/netstandard2.0/en/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.xml"]}, "NAudio/2.2.1": {"sha512": "c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "type": "package", "path": "naudio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.dll", "lib/net472/NAudio.xml", "lib/net6.0-windows7.0/NAudio.dll", "lib/net6.0-windows7.0/NAudio.xml", "lib/net6.0/NAudio.dll", "lib/net6.0/NAudio.xml", "lib/netcoreapp3.1/NAudio.dll", "lib/netcoreapp3.1/NAudio.xml", "license.txt", "naudio-icon.png", "naudio.2.2.1.nupkg.sha512", "naudio.nuspec"]}, "NAudio.Asio/2.2.1": {"sha512": "hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "type": "package", "path": "naudio.asio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Asio.dll", "lib/netstandard2.0/NAudio.Asio.xml", "naudio-icon.png", "naudio.asio.2.2.1.nupkg.sha512", "naudio.asio.nuspec"]}, "NAudio.Core/2.2.1": {"sha512": "GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "type": "package", "path": "naudio.core/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Core.dll", "lib/netstandard2.0/NAudio.Core.xml", "naudio-icon.png", "naudio.core.2.2.1.nupkg.sha512", "naudio.core.nuspec"]}, "NAudio.Midi/2.2.1": {"sha512": "6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "type": "package", "path": "naudio.midi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Midi.dll", "lib/netstandard2.0/NAudio.Midi.xml", "naudio-icon.png", "naudio.midi.2.2.1.nupkg.sha512", "naudio.midi.nuspec"]}, "NAudio.Wasapi/2.2.1": {"sha512": "lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "type": "package", "path": "naudio.wasapi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Wasapi.dll", "lib/netstandard2.0/NAudio.Wasapi.xml", "lib/uap10.0.18362/NAudio.Wasapi.dll", "lib/uap10.0.18362/NAudio.Wasapi.pri", "lib/uap10.0.18362/NAudio.Wasapi.xml", "naudio-icon.png", "naudio.wasapi.2.2.1.nupkg.sha512", "naudio.wasapi.nuspec"]}, "NAudio.WinForms/2.2.1": {"sha512": "DlDkewY1myY0A+3NrYRJD+MZhZV0yy1mNF6dckB27IQ9XCs/My5Ip8BZcoSHOsaPSe2GAjvoaDnk6N9w8xTv7w==", "type": "package", "path": "naudio.winforms/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.WinForms.dll", "lib/net472/NAudio.WinForms.xml", "lib/netcoreapp3.1/NAudio.WinForms.dll", "lib/netcoreapp3.1/NAudio.WinForms.xml", "naudio-icon.png", "naudio.winforms.2.2.1.nupkg.sha512", "naudio.winforms.nuspec"]}, "NAudio.WinMM/2.2.1": {"sha512": "xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "type": "package", "path": "naudio.winmm/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.WinMM.dll", "lib/netstandard2.0/NAudio.WinMM.xml", "naudio-icon.png", "naudio.winmm.2.2.1.nupkg.sha512", "naudio.winmm.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/7.0.2": {"sha512": "/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "type": "package", "path": "system.management/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.2.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"sha512": "J4GUi3xZQLUBasNwZnjrffN8i5wpHrBtZoLG+OhRyGo/+YunMRWWtwoMDlUAIdmX0uRfpHIBDSV6zyr3yf00TA==", "type": "package", "path": "system.runtime.interopservices.windowsruntime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "lib/netstandard1.3/System.Runtime.InteropServices.WindowsRuntime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netcore50/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "system.runtime.interopservices.windowsruntime.4.3.0.nupkg.sha512", "system.runtime.interopservices.windowsruntime.nuspec"]}, "System.Runtime.WindowsRuntime/4.6.0": {"sha512": "IWrs1TmbxP65ZZjIglNyvDkFNoV5q2Pofg5WO7I8RKQOpLdFprQSh3xesOoClBqR4JHr4nEB1Xk1MqLPW1jPuQ==", "type": "package", "path": "system.runtime.windowsruntime/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.targets", "build/net451/System.Runtime.WindowsRuntime.targets", "build/net461/System.Runtime.WindowsRuntime.targets", "buildTransitive/net45/System.Runtime.WindowsRuntime.targets", "buildTransitive/net451/System.Runtime.WindowsRuntime.targets", "buildTransitive/net461/System.Runtime.WindowsRuntime.targets", "lib/net45/_._", "lib/netstandard1.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.0/System.Runtime.WindowsRuntime.xml", "lib/netstandard1.2/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.2/System.Runtime.WindowsRuntime.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.xml", "lib/portable-win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.dll", "ref/netcore50/System.Runtime.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.2/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.xml", "ref/portable-win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"sha512": "r4tNw5v5kqRJ9HikWpcyNf3suGw7DjX93svj9iBjtdeLqL8jt9Z+7f+s4wrKZJr84u8IMsrIjt8K6jYvkRqMSg==", "type": "package", "path": "system.runtime.windowsruntime.ui.xaml/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets", "build/net461/System.Runtime.WindowsRuntime.UI.Xaml.targets", "lib/net45/_._", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/portable-win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/portable-win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.ui.xaml.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["Microsoft.Toolkit.Win32.UI.Controls >= 6.1.3", "Microsoft.Windows.SDK.Contracts >= 10.0.22621.2428", "NAudio >= 2.2.1", "Newtonsoft.Json >= 13.0.3", "System.Management >= 7.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\customUI\\WindowsCustom.csproj", "projectName": "WindowsCustom", "projectPath": "C:\\Users\\<USER>\\customUI\\WindowsCustom.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\customUI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.Toolkit.Win32.UI.Controls": {"target": "Package", "version": "[6.1.3, )"}, "Microsoft.Windows.SDK.Contracts": {"target": "Package", "version": "[10.0.22621.2428, )"}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1102", "level": "Error", "message": "Unable to find package Microsoft.Toolkit.Win32.UI.Controls with version (>= 6.1.3)\r\n  - Found 9 version(s) in nuget.org [ Nearest version: 5.0.0 ]", "libraryId": "Microsoft.Toolkit.Win32.UI.Controls", "targetGraphs": ["net9.0-windows7.0"]}]}