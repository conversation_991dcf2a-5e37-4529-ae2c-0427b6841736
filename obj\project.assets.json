{"version": 3, "targets": {"net9.0-windows7.0": {"Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "NAudio/2.2.1": {"type": "package", "dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinForms": "2.2.1", "NAudio.WinMM": "2.2.1"}, "compile": {"lib/net6.0-windows7.0/NAudio.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/NAudio.dll": {"related": ".xml"}}}, "NAudio.Asio/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}}, "NAudio.Core/2.2.1": {"type": "package", "compile": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}}, "NAudio.Midi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}}, "NAudio.Wasapi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}}, "NAudio.WinForms/2.2.1": {"type": "package", "dependencies": {"NAudio.WinMM": "2.2.1"}, "compile": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "NAudio.WinMM/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "NAudio/2.2.1": {"sha512": "c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "type": "package", "path": "naudio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.dll", "lib/net472/NAudio.xml", "lib/net6.0-windows7.0/NAudio.dll", "lib/net6.0-windows7.0/NAudio.xml", "lib/net6.0/NAudio.dll", "lib/net6.0/NAudio.xml", "lib/netcoreapp3.1/NAudio.dll", "lib/netcoreapp3.1/NAudio.xml", "license.txt", "naudio-icon.png", "naudio.2.2.1.nupkg.sha512", "naudio.nuspec"]}, "NAudio.Asio/2.2.1": {"sha512": "hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "type": "package", "path": "naudio.asio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Asio.dll", "lib/netstandard2.0/NAudio.Asio.xml", "naudio-icon.png", "naudio.asio.2.2.1.nupkg.sha512", "naudio.asio.nuspec"]}, "NAudio.Core/2.2.1": {"sha512": "GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "type": "package", "path": "naudio.core/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Core.dll", "lib/netstandard2.0/NAudio.Core.xml", "naudio-icon.png", "naudio.core.2.2.1.nupkg.sha512", "naudio.core.nuspec"]}, "NAudio.Midi/2.2.1": {"sha512": "6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "type": "package", "path": "naudio.midi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Midi.dll", "lib/netstandard2.0/NAudio.Midi.xml", "naudio-icon.png", "naudio.midi.2.2.1.nupkg.sha512", "naudio.midi.nuspec"]}, "NAudio.Wasapi/2.2.1": {"sha512": "lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "type": "package", "path": "naudio.wasapi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Wasapi.dll", "lib/netstandard2.0/NAudio.Wasapi.xml", "lib/uap10.0.18362/NAudio.Wasapi.dll", "lib/uap10.0.18362/NAudio.Wasapi.pri", "lib/uap10.0.18362/NAudio.Wasapi.xml", "naudio-icon.png", "naudio.wasapi.2.2.1.nupkg.sha512", "naudio.wasapi.nuspec"]}, "NAudio.WinForms/2.2.1": {"sha512": "DlDkewY1myY0A+3NrYRJD+MZhZV0yy1mNF6dckB27IQ9XCs/My5Ip8BZcoSHOsaPSe2GAjvoaDnk6N9w8xTv7w==", "type": "package", "path": "naudio.winforms/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.WinForms.dll", "lib/net472/NAudio.WinForms.xml", "lib/netcoreapp3.1/NAudio.WinForms.dll", "lib/netcoreapp3.1/NAudio.WinForms.xml", "naudio-icon.png", "naudio.winforms.2.2.1.nupkg.sha512", "naudio.winforms.nuspec"]}, "NAudio.WinMM/2.2.1": {"sha512": "xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "type": "package", "path": "naudio.winmm/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.WinMM.dll", "lib/netstandard2.0/NAudio.WinMM.xml", "naudio-icon.png", "naudio.winmm.2.2.1.nupkg.sha512", "naudio.winmm.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["NAudio >= 2.2.1", "Newtonsoft.Json >= 13.0.3", "System.Management >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\customUI\\WindowsCustom.csproj", "projectName": "WindowsCustom", "projectPath": "C:\\Users\\<USER>\\customUI\\WindowsCustom.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\customUI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"NAudio": {"target": "Package", "version": "[2.2.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}