#!/usr/bin/env python3
"""
Windows-style Start Menu for WindowsCustom
"""

import tkinter as tk
from tkinter import ttk
import subprocess
import os
import json

class StartMenu:
    def __init__(self, parent_root):
        self.parent_root = parent_root
        self.menu_window = None
        self.is_open = False
        
    def toggle_menu(self):
        """Toggle the start menu open/closed"""
        if self.is_open:
            self.close_menu()
        else:
            self.open_menu()
    
    def open_menu(self):
        """Open the start menu"""
        if self.menu_window:
            self.close_menu()
        
        # Create menu window
        self.menu_window = tk.Toplevel(self.parent_root)
        self.menu_window.title("")
        self.menu_window.overrideredirect(True)
        self.menu_window.configure(bg="#2D2D2D")
        
        # Position at bottom-left (above taskbar)
        screen_height = self.parent_root.winfo_screenheight()
        taskbar_height = 48
        menu_height = 400
        menu_width = 300
        
        x = 10
        y = screen_height - taskbar_height - menu_height - 10
        
        self.menu_window.geometry(f"{menu_width}x{menu_height}+{x}+{y}")
        self.menu_window.wm_attributes("-topmost", True)
        
        # Add border
        self.menu_window.configure(relief="raised", bd=2)
        
        self.create_menu_content()
        self.is_open = True
        
        # Close menu when clicking outside
        self.menu_window.bind("<FocusOut>", lambda e: self.close_menu())
        self.menu_window.focus_set()
    
    def close_menu(self):
        """Close the start menu"""
        if self.menu_window:
            self.menu_window.destroy()
            self.menu_window = None
        self.is_open = False

    def get_all_installed_apps(self):
        """Get all installed applications on the system"""
        apps = []

        # Common Windows applications with their commands
        common_apps = [
            ("🌐", "Chrome", "chrome"),
            ("💻", "VSCode", "code"),
            ("📁", "File Explorer", "explorer"),
            ("📝", "Notepad", "notepad"),
            ("🧮", "Calculator", "calc"),
            ("⚙️", "Settings", "ms-settings:"),
            ("🎨", "Paint", "mspaint"),
            ("📊", "Excel", "excel"),
            ("📄", "Word", "winword"),
            ("📈", "PowerPoint", "powerpnt"),
            ("🎵", "Windows Media Player", "wmplayer"),
            ("📷", "Camera", "microsoft.windows.camera:"),
            ("📧", "Mail", "outlookmail:"),
            ("🗓️", "Calendar", "outlookcal:"),
            ("🛒", "Microsoft Store", "ms-windows-store:"),
            ("⚡", "PowerShell", "powershell"),
            ("⚫", "Command Prompt", "cmd"),
            ("🔧", "Control Panel", "control"),
            ("💾", "Disk Cleanup", "cleanmgr"),
            ("🖥️", "Task Manager", "taskmgr"),
            ("🔍", "Registry Editor", "regedit"),
            ("🌍", "Edge", "msedge"),
            ("🦊", "Firefox", "firefox"),
            ("💬", "Discord", "discord"),
            ("🎮", "Steam", "steam"),
            ("📱", "Phone Link", "ms-yourphone:"),
            ("🎬", "Movies & TV", "mswindowsvideo:"),
            ("🎶", "Groove Music", "mswindowsmusic:"),
            ("📸", "Photos", "ms-photos:"),
            ("🗺️", "Maps", "bingmaps:"),
            ("☁️", "OneDrive", "onedrive"),
            ("📋", "Clipboard", "ms-clipchamp:"),
            ("🔐", "BitLocker", "manage-bde"),
            ("🖨️", "Print 3D", "ms-print3d:"),
            ("📱", "Your Phone", "ms-yourphone:"),
            ("🎯", "Feedback Hub", "feedback-hub:"),
            ("🔄", "Sync Center", "mobsync"),
            ("🎪", "Mixed Reality Portal", "ms-holographicfirstrun:"),
            ("📺", "Xbox", "xbox:"),
            ("🎮", "Xbox Game Bar", "ms-gamebar:"),
            ("🔊", "Volume Mixer", "sndvol"),
            ("🖼️", "Snipping Tool", "snippingtool"),
            ("📐", "Math Input Panel", "mip"),
            ("🔤", "Character Map", "charmap"),
            ("🎨", "Paint 3D", "ms-paint:"),
            ("📊", "Performance Monitor", "perfmon"),
            ("🔍", "Resource Monitor", "resmon"),
            ("🖥️", "System Information", "msinfo32"),
            ("🔧", "Device Manager", "devmgmt.msc"),
            ("💻", "Computer Management", "compmgmt.msc"),
            ("🔐", "Local Security Policy", "secpol.msc"),
            ("👥", "Local Users and Groups", "lusrmgr.msc"),
            ("📋", "Event Viewer", "eventvwr"),
            ("🔄", "Services", "services.msc"),
            ("📊", "Disk Management", "diskmgmt.msc"),
            ("🌐", "Internet Options", "inetcpl.cpl"),
            ("🖱️", "Mouse Properties", "main.cpl"),
            ("⌨️", "Keyboard Properties", "main.cpl keyboard"),
            ("🔊", "Sound Properties", "mmsys.cpl"),
            ("🖥️", "Display Properties", "desk.cpl"),
            ("⏰", "Date and Time", "timedate.cpl"),
            ("🌍", "Region", "intl.cpl"),
            ("🔌", "Power Options", "powercfg.cpl"),
            ("📶", "Network Connections", "ncpa.cpl"),
            ("🔥", "Windows Firewall", "firewall.cpl"),
            ("➕", "Add/Remove Programs", "appwiz.cpl"),
            ("👤", "User Accounts", "netplwiz"),
            ("🎮", "Game Controllers", "joy.cpl"),
            ("📞", "Phone and Modem", "telephon.cpl"),
            ("🖨️", "Printers", "printmanagement.msc"),
            ("💾", "Backup and Restore", "sdclt"),
            ("🔄", "System Restore", "rstrui"),
            ("🧹", "Disk Cleanup", "cleanmgr"),
            ("🔍", "System File Checker", "sfc"),
            ("💿", "Windows Memory Diagnostic", "mdsched"),
            ("🔧", "System Configuration", "msconfig"),
            ("📊", "Reliability Monitor", "perfmon /rel"),
            ("🔍", "Windows Troubleshooting", "control /name Microsoft.Troubleshooting")
        ]

        # Add WindowsCustom specific apps
        windowscustom_apps = [
            ("🎨", "WindowsCustom Settings", "python settings_gui.py"),
            ("🎵", "Music Controls", "python music_controls.py"),
            ("🎯", "Theme Test", "python test_themes.py"),
            ("🖥️", "Taskbar Demo", "python demo_taskbar.py"),
            ("🔧", "System Test", "python test_system.py")
        ]

        # Combine all apps
        apps.extend(common_apps)
        apps.extend(windowscustom_apps)

        return apps
    
    def create_menu_content(self):
        """Create the start menu content"""
        # Header
        header_frame = tk.Frame(self.menu_window, bg="#66BFF2", height=60)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)
        
        user_label = tk.Label(header_frame, text=f"👤 {os.getenv('USERNAME', 'User')}", 
                             font=("Segoe UI", 12, "bold"), 
                             bg="#66BFF2", fg="#1E3A8A")
        user_label.pack(side="left", padx=10, pady=15)
        
        # WindowsCustom label
        custom_label = tk.Label(header_frame, text="WindowsCustom", 
                               font=("Segoe UI", 10), 
                               bg="#66BFF2", fg="#39C5BB")
        custom_label.pack(side="right", padx=10, pady=15)
        
        # Main content area
        content_frame = tk.Frame(self.menu_window, bg="#2D2D2D")
        content_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # All Apps section
        apps_label = tk.Label(content_frame, text="📱 All Apps",
                             font=("Segoe UI", 10, "bold"),
                             bg="#2D2D2D", fg="#FFFFFF")
        apps_label.pack(anchor="w", padx=5, pady=5)

        # Scrollable app list
        apps_canvas = tk.Canvas(content_frame, bg="#2D2D2D", height=200, highlightthickness=0)
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=apps_canvas.yview)
        scrollable_frame = tk.Frame(apps_canvas, bg="#2D2D2D")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: apps_canvas.configure(scrollregion=apps_canvas.bbox("all"))
        )

        apps_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        apps_canvas.configure(yscrollcommand=scrollbar.set)

        apps_canvas.pack(side="left", fill="both", expand=True, padx=5)
        scrollbar.pack(side="right", fill="y")

        # Get all installed applications
        all_apps = self.get_all_installed_apps()

        # Create app buttons in grid
        apps_per_row = 3
        for i, (icon, name, command) in enumerate(all_apps):
            row = i // apps_per_row
            col = i % apps_per_row

            app_btn = tk.Button(scrollable_frame, text=f"{icon}\n{name}",
                               font=("Segoe UI", 8), width=8, height=3,
                               bg="#404040", fg="#FFFFFF",
                               relief="flat", bd=1,
                               command=lambda cmd=command: self.launch_app(cmd))
            app_btn.grid(row=row, column=col, padx=2, pady=2, sticky="nsew")

            # Hover effects
            app_btn.bind("<Enter>", lambda e, btn=app_btn: btn.configure(bg="#505050"))
            app_btn.bind("<Leave>", lambda e, btn=app_btn: btn.configure(bg="#404040"))

        # Configure grid weights
        for i in range(apps_per_row):
            scrollable_frame.columnconfigure(i, weight=1)
        
        # Separator
        separator = tk.Frame(content_frame, height=1, bg="#555555")
        separator.pack(fill="x", padx=5, pady=10)
        
        # System section
        system_label = tk.Label(content_frame, text="🔧 System", 
                               font=("Segoe UI", 10, "bold"),
                               bg="#2D2D2D", fg="#FFFFFF")
        system_label.pack(anchor="w", padx=5, pady=5)
        
        # System buttons
        system_frame = tk.Frame(content_frame, bg="#2D2D2D")
        system_frame.pack(fill="x", padx=5)
        
        system_items = [
            ("⚙️ WindowsCustom Settings", lambda: self.launch_app("python settings_gui.py")),
            ("🎵 Music Controls", self.show_music_controls),
            ("🔄 Restart Taskbar", self.restart_taskbar),
            ("🚪 Exit WindowsCustom", self.exit_windowscustom)
        ]
        
        for text, command in system_items:
            btn = tk.Button(system_frame, text=text, 
                           font=("Segoe UI", 9), 
                           bg="#404040", fg="#FFFFFF",
                           relief="flat", bd=1, anchor="w",
                           command=command)
            btn.pack(fill="x", pady=1)
            
            # Hover effects
            btn.bind("<Enter>", lambda e, b=btn: b.configure(bg="#505050"))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(bg="#404040"))
        
        # Power options at bottom
        power_frame = tk.Frame(self.menu_window, bg="#2D2D2D", height=40)
        power_frame.pack(fill="x", side="bottom")
        power_frame.pack_propagate(False)
        
        power_btn = tk.Button(power_frame, text="⏻ Power", 
                             font=("Segoe UI", 9),
                             bg="#404040", fg="#FFFFFF",
                             relief="flat", bd=1,
                             command=self.show_power_menu)
        power_btn.pack(side="right", padx=10, pady=5)
    
    def launch_app(self, command):
        """Launch an application"""
        try:
            if command.startswith("python"):
                subprocess.Popen(command.split())
            else:
                subprocess.Popen(command, shell=True)
            self.close_menu()
        except Exception as e:
            print(f"Failed to launch {command}: {e}")
    
    def show_music_controls(self):
        """Show music control popup"""
        self.close_menu()
        
        # Create music control window
        music_window = tk.Toplevel(self.parent_root)
        music_window.title("Music Controls")
        music_window.geometry("300x150")
        music_window.configure(bg="#2D2D2D")
        music_window.wm_attributes("-topmost", True)
        
        # Center the window
        music_window.transient(self.parent_root)
        music_window.grab_set()
        
        tk.Label(music_window, text="🎵 Music Controls", 
                font=("Segoe UI", 14, "bold"),
                bg="#2D2D2D", fg="#66BFF2").pack(pady=10)
        
        # Control buttons
        controls_frame = tk.Frame(music_window, bg="#2D2D2D")
        controls_frame.pack(pady=10)
        
        tk.Button(controls_frame, text="⏸️ Pause", 
                 bg="#404040", fg="#FFFFFF", font=("Segoe UI", 10),
                 command=lambda: self.music_control("pause")).pack(side="left", padx=5)
        
        tk.Button(controls_frame, text="▶️ Play", 
                 bg="#404040", fg="#FFFFFF", font=("Segoe UI", 10),
                 command=lambda: self.music_control("play")).pack(side="left", padx=5)
        
        tk.Button(controls_frame, text="⏹️ Stop", 
                 bg="#404040", fg="#FFFFFF", font=("Segoe UI", 10),
                 command=lambda: self.music_control("stop")).pack(side="left", padx=5)
        
        tk.Button(music_window, text="Close", 
                 bg="#666666", fg="#FFFFFF", font=("Segoe UI", 9),
                 command=music_window.destroy).pack(pady=10)
    
    def music_control(self, action):
        """Control music playback"""
        # This would interface with the music player
        print(f"Music control: {action}")
        # TODO: Implement actual music control interface
    
    def restart_taskbar(self):
        """Restart the taskbar"""
        self.close_menu()
        print("Restarting taskbar...")
        # TODO: Implement taskbar restart
    
    def exit_windowscustom(self):
        """Exit WindowsCustom system"""
        self.close_menu()
        self.parent_root.quit()
    
    def show_power_menu(self):
        """Show power options menu"""
        # Simple power menu
        power_window = tk.Toplevel(self.parent_root)
        power_window.title("Power Options")
        power_window.geometry("200x120")
        power_window.configure(bg="#2D2D2D")
        power_window.wm_attributes("-topmost", True)
        
        tk.Label(power_window, text="⏻ Power Options", 
                font=("Segoe UI", 12, "bold"),
                bg="#2D2D2D", fg="#FFFFFF").pack(pady=10)
        
        tk.Button(power_window, text="🔄 Restart", 
                 bg="#404040", fg="#FFFFFF",
                 command=lambda: os.system("shutdown /r /t 0")).pack(pady=2)
        
        tk.Button(power_window, text="⏻ Shutdown", 
                 bg="#404040", fg="#FFFFFF",
                 command=lambda: os.system("shutdown /s /t 0")).pack(pady=2)
        
        tk.Button(power_window, text="Cancel", 
                 bg="#666666", fg="#FFFFFF",
                 command=power_window.destroy).pack(pady=5)

# Global start menu instance
start_menu = None

def init_start_menu(parent_root):
    """Initialize the start menu"""
    global start_menu
    start_menu = StartMenu(parent_root)
    return start_menu

def toggle_start_menu():
    """Toggle the start menu"""
    if start_menu:
        start_menu.toggle_menu()
