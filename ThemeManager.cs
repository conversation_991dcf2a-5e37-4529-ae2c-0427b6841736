using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Media;

namespace WindowsCustom
{
    public class ThemeManager
    {
        private Dictionary<string, Theme> themes;

        public ThemeManager()
        {
            InitializeThemes();
        }

        private void InitializeThemes()
        {
            themes = new Dictionary<string, Theme>
            {
                ["HatsuneMiku"] = new Theme
                {
                    Name = "Hatsune Miku",
                    TaskbarBackground = new SolidColorBrush(Color.FromRgb(102, 191, 242)), // #66BFF2
                    ButtonBackground = new SolidColorBrush(Color.FromRgb(57, 197, 187)),   // #39C5BB
                    ButtonForeground = new SolidColorBrush(Color.FromRgb(30, 58, 138)),    // #1E3A8A
                    ButtonHover = new SolidColorBrush(Color.FromRgb(77, 207, 197)),        // Lighter teal
                    ButtonPressed = new SolidColorBrush(Color.FromRgb(37, 177, 167)),      // Darker teal
                    ButtonBorder = new SolidColorBrush(Color.FromRgb(30, 58, 138)),
                    TextForeground = new SolidColorBrush(Colors.White)
                },
                
                ["Red"] = new Theme
                {
                    Name = "Red",
                    TaskbarBackground = new SolidColorBrush(Color.FromRgb(255, 107, 107)), // #FF6B6B
                    ButtonBackground = new SolidColorBrush(Color.FromRgb(204, 85, 85)),    // #CC5555
                    ButtonForeground = new SolidColorBrush(Colors.White),
                    ButtonHover = new SolidColorBrush(Color.FromRgb(224, 95, 95)),         // Lighter red
                    ButtonPressed = new SolidColorBrush(Color.FromRgb(184, 75, 75)),       // Darker red
                    ButtonBorder = new SolidColorBrush(Color.FromRgb(184, 75, 75)),
                    TextForeground = new SolidColorBrush(Colors.White)
                },
                
                ["WindowsDark"] = new Theme
                {
                    Name = "Windows Dark",
                    TaskbarBackground = new SolidColorBrush(Color.FromRgb(31, 31, 31)),    // #1F1F1F
                    ButtonBackground = new SolidColorBrush(Color.FromRgb(45, 45, 45)),     // #2D2D2D
                    ButtonForeground = new SolidColorBrush(Colors.White),
                    ButtonHover = new SolidColorBrush(Color.FromRgb(65, 65, 65)),          // Lighter gray
                    ButtonPressed = new SolidColorBrush(Color.FromRgb(25, 25, 25)),        // Darker gray
                    ButtonBorder = new SolidColorBrush(Color.FromRgb(65, 65, 65)),
                    TextForeground = new SolidColorBrush(Colors.White)
                },
                
                ["Ocean"] = new Theme
                {
                    Name = "Ocean",
                    TaskbarBackground = new SolidColorBrush(Color.FromRgb(52, 152, 219)),  // Ocean blue
                    ButtonBackground = new SolidColorBrush(Color.FromRgb(41, 128, 185)),   // Darker blue
                    ButtonForeground = new SolidColorBrush(Colors.White),
                    ButtonHover = new SolidColorBrush(Color.FromRgb(62, 172, 239)),        // Lighter blue
                    ButtonPressed = new SolidColorBrush(Color.FromRgb(31, 108, 165)),      // Darker blue
                    ButtonBorder = new SolidColorBrush(Color.FromRgb(31, 108, 165)),
                    TextForeground = new SolidColorBrush(Colors.White)
                },
                
                ["Purple"] = new Theme
                {
                    Name = "Purple",
                    TaskbarBackground = new SolidColorBrush(Color.FromRgb(155, 89, 182)),  // Purple
                    ButtonBackground = new SolidColorBrush(Color.FromRgb(142, 68, 173)),   // Darker purple
                    ButtonForeground = new SolidColorBrush(Colors.White),
                    ButtonHover = new SolidColorBrush(Color.FromRgb(175, 109, 202)),       // Lighter purple
                    ButtonPressed = new SolidColorBrush(Color.FromRgb(122, 48, 153)),      // Darker purple
                    ButtonBorder = new SolidColorBrush(Color.FromRgb(122, 48, 153)),
                    TextForeground = new SolidColorBrush(Colors.White)
                }
            };
        }

        public void ApplyTheme(Window window, string themeName)
        {
            if (!themes.ContainsKey(themeName))
                themeName = "HatsuneMiku"; // Default fallback

            var theme = themes[themeName];
            
            // Apply theme resources to window
            window.Resources["TaskbarBackground"] = theme.TaskbarBackground;
            window.Resources["ButtonBackground"] = theme.ButtonBackground;
            window.Resources["ButtonForeground"] = theme.ButtonForeground;
            window.Resources["ButtonHover"] = theme.ButtonHover;
            window.Resources["ButtonPressed"] = theme.ButtonPressed;
            window.Resources["ButtonBorder"] = theme.ButtonBorder;
            window.Resources["TextForeground"] = theme.TextForeground;
        }

        public List<string> GetAvailableThemes()
        {
            return new List<string>(themes.Keys);
        }

        public Theme GetTheme(string themeName)
        {
            return themes.ContainsKey(themeName) ? themes[themeName] : themes["HatsuneMiku"];
        }
    }

    public class Theme
    {
        public string Name { get; set; }
        public SolidColorBrush TaskbarBackground { get; set; }
        public SolidColorBrush ButtonBackground { get; set; }
        public SolidColorBrush ButtonForeground { get; set; }
        public SolidColorBrush ButtonHover { get; set; }
        public SolidColorBrush ButtonPressed { get; set; }
        public SolidColorBrush ButtonBorder { get; set; }
        public SolidColorBrush TextForeground { get; set; }
    }
}
