using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;

namespace WindowsCustom
{
    public class TaskbarManager
    {
        private const int SW_HIDE = 0;
        private const int SW_SHOW = 5;
        private const int SW_RESTORE = 9;

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern int GetWindowTextLength(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        private IntPtr taskbarHandle;

        public TaskbarManager()
        {
            taskbarHandle = FindWindow("Shell_TrayWnd", null);
        }

        public void HideWindowsTaskbar()
        {
            if (taskbarHandle != IntPtr.Zero)
            {
                ShowWindow(taskbarHandle, SW_HIDE);
            }
        }

        public void ShowWindowsTaskbar()
        {
            if (taskbarHandle != IntPtr.Zero)
            {
                ShowWindow(taskbarHandle, SW_SHOW);
            }
        }

        public List<RunningApp> GetRunningApplications()
        {
            var apps = new List<RunningApp>();
            
            EnumWindows((hWnd, lParam) =>
            {
                if (IsWindowVisible(hWnd) && GetWindowTextLength(hWnd) > 0)
                {
                    var title = GetWindowTitle(hWnd);
                    if (!string.IsNullOrEmpty(title) && !IsSystemWindow(title))
                    {
                        GetWindowThreadProcessId(hWnd, out uint processId);
                        try
                        {
                            var process = Process.GetProcessById((int)processId);
                            apps.Add(new RunningApp
                            {
                                Handle = hWnd,
                                Title = title,
                                ProcessName = process.ProcessName
                            });
                        }
                        catch
                        {
                            // Process might have exited, skip
                        }
                    }
                }
                return true;
            }, IntPtr.Zero);

            return apps;
        }

        private string GetWindowTitle(IntPtr hWnd)
        {
            var length = GetWindowTextLength(hWnd);
            if (length == 0) return string.Empty;

            var builder = new StringBuilder(length + 1);
            GetWindowText(hWnd, builder, builder.Capacity);
            return builder.ToString();
        }

        private bool IsSystemWindow(string title)
        {
            var systemTitles = new[]
            {
                "Program Manager",
                "Desktop Window Manager",
                "Windows Input Experience",
                "Microsoft Text Input Application",
                "Settings",
                "Cortana"
            };

            foreach (var systemTitle in systemTitles)
            {
                if (title.Contains(systemTitle))
                    return true;
            }

            return false;
        }

        public void SwitchToApplication(IntPtr handle)
        {
            try
            {
                ShowWindow(handle, SW_RESTORE);
                SetForegroundWindow(handle);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error switching to application: {ex.Message}");
            }
        }
    }
}
