using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using NAudio.Wave;

namespace WindowsCustom
{
    public class MusicPlayer : IDisposable
    {
        private IWavePlayer wavePlayer;
        private AudioFileReader audioFileReader;
        private List<string> playlist;
        private int currentTrackIndex = 0;
        private bool isPlaying = false;
        private bool isPaused = false;

        public event Action<string> TrackChanged;
        public event Action<bool> PlayStateChanged;

        public MusicPlayer()
        {
            LoadPlaylist();
        }

        private void LoadPlaylist()
        {
            playlist = new List<string>();
            var musicFolder = Path.Combine(Environment.CurrentDirectory, "music");
            
            if (Directory.Exists(musicFolder))
            {
                var supportedExtensions = new[] { ".mp3", ".wav", ".flac", ".m4a" };
                playlist = Directory.GetFiles(musicFolder)
                    .Where(file => supportedExtensions.Contains(Path.GetExtension(file).ToLower()))
                    .ToList();
            }
        }

        public void Play()
        {
            try
            {
                if (isPaused && wavePlayer != null)
                {
                    wavePlayer.Play();
                    isPaused = false;
                    isPlaying = true;
                    PlayStateChanged?.Invoke(true);
                    return;
                }

                if (playlist.Count == 0)
                {
                    LoadPlaylist();
                    if (playlist.Count == 0) return;
                }

                Stop();

                var currentTrack = playlist[currentTrackIndex];
                audioFileReader = new AudioFileReader(currentTrack);
                wavePlayer = new WaveOutEvent();
                
                wavePlayer.Init(audioFileReader);
                wavePlayer.PlaybackStopped += OnPlaybackStopped;
                wavePlayer.Play();

                isPlaying = true;
                isPaused = false;
                
                TrackChanged?.Invoke(Path.GetFileNameWithoutExtension(currentTrack));
                PlayStateChanged?.Invoke(true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing music: {ex.Message}");
            }
        }

        public void Pause()
        {
            if (wavePlayer != null && isPlaying)
            {
                wavePlayer.Pause();
                isPaused = true;
                isPlaying = false;
                PlayStateChanged?.Invoke(false);
            }
        }

        public void Stop()
        {
            wavePlayer?.Stop();
            wavePlayer?.Dispose();
            audioFileReader?.Dispose();
            
            wavePlayer = null;
            audioFileReader = null;
            isPlaying = false;
            isPaused = false;
            
            PlayStateChanged?.Invoke(false);
        }

        public void Next()
        {
            if (playlist.Count == 0) return;
            
            currentTrackIndex = (currentTrackIndex + 1) % playlist.Count;
            if (isPlaying || isPaused)
            {
                Play();
            }
        }

        public void Previous()
        {
            if (playlist.Count == 0) return;
            
            currentTrackIndex = currentTrackIndex == 0 ? playlist.Count - 1 : currentTrackIndex - 1;
            if (isPlaying || isPaused)
            {
                Play();
            }
        }

        private void OnPlaybackStopped(object sender, StoppedEventArgs e)
        {
            if (e.Exception == null)
            {
                // Track finished naturally, play next
                Next();
            }
            else
            {
                // Error occurred
                Stop();
            }
        }

        public string GetCurrentTrack()
        {
            if (playlist.Count == 0) return "No music loaded";
            return Path.GetFileNameWithoutExtension(playlist[currentTrackIndex]);
        }

        public bool IsPlaying => isPlaying;
        public bool IsPaused => isPaused;

        public void Dispose()
        {
            Stop();
        }
    }
}
